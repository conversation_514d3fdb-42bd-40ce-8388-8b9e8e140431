# Inteli - Instituto de Tecnologia e Liderança 

<p align="center">
<a href= "https://www.inteli.edu.br/"><img src="/assets/inteli.png" alt="Inteli - Instituto de Tecnologia e Liderança" border="0"></a>
</p>

# Projeto: Conferidas+


## Grupo 3



## :student: Integrantes: 
- <a href="https://www.linkedin.com/in/livianegrini/"><PERSON><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/bruno-frossard"><PERSON></a>
- <a href="https://www.linkedin.com/in/maurihkorn1818/"><PERSON><PERSON></a>
- <a href="https://www.linkedin.com/in/ariel-mouadeb-71686624a/"> <PERSON> 7</a>
- <a href="https://www.linkedin.com/in/nathaliafigueredo"><PERSON><PERSON><PERSON><PERSON>redo</a> 
- <a href="https://www.linkedin.com/in/rafael-figueiredo-campos-72131735b/"><PERSON></a>
- <a href="https://www.linkedin.com/in/wendel-feitosa-975bb1346/">Wendel Feitosa</a>

## :teacher: Professores:
### Orientador(a) 
- <a href="https://www.linkedin.com/in/laizaribeiro/">Laíza Ribeiro</a>
### Instrutores
- <a href="https://www.linkedin.com/in/afonsolelis/">Afonso Brandão</a>
- <a href="https://www.linkedin.com/in/francisco-escobar/">Francisco Escobar</a> 
- <a href="https://www.linkedin.com/in/fernando-pizzo-208b526a/">Fernando Pizzo</a> 
- <a href="https://www.linkedin.com/in/natalia-k-37a62052/">Natália Kloeckner</a>
- <a href="https://www.linkedin.com/in/marcelo-gon%C3%A7alves-phd-a550652/">Marcelo Luiz Amaral</a>

## 📝 Descrição

Pacientes com feridas crônicas enfrentam uma série de desafios que vão além da própria condição física. A dificuldade de locomoção, especialmente em regiões afastadas dos grandes centros urbanos, limita o acesso a consultas regulares nas Unidades Básicas de Saúde (UBS), o que agrava ainda mais a situação. Frente a esse cenário, surge o Conferidas+ para facilitar o acompanhamento remoto, de forma acessível e eficiente, fortalecendo o vínculo entre os pacientes e as UBS. Desenvolvemos um sistema web com duas interfaces principais: uma voltada para os pacientes e outra para os profissionais de saúde. A ferramenta permitirá que os pacientes registrem, de forma simples e segura, informações sobre suas feridas, incluindo imagens, vídeos, áudios ou descrições em texto. Esse fluxo de informações será fundamental para que as equipes de saúde possam realizar um monitoramento remoto contínuo, identificar sinais de alerta e orientar os pacientes de forma personalizada.

O processo de uso será iniciado pelos agentes de saúde, que farão o cadastro dos pacientes no sistema durante as visitas domiciliares ou atendimentos presenciais nas UBS. A partir disso, os pacientes poderão, de suas próprias casas, enviar atualizações periódicas sobre a evolução de suas feridas. 


## 📝 Link de demonstração

_Coloque aqui o link para seu projeto publicado e link para vídeo de demonstração_

## 📁 Estrutura de pastas

Dentre os arquivos e pastas presentes na raiz do projeto, definem-se:

- <b>assets</b>: aqui estão os arquivos relacionados a elementos não-estruturados deste repositório, como imagens.

- <b>document</b>: aqui estão todos os documentos do projeto, como o Web Application  Document (WAD) bem como documentos complementares, na pasta "other".

- <b>src</b>: Todo o código fonte criado para o desenvolvimento do projeto de aplicação web.

- <b>README.md</b>: arquivo que serve como guia introdutório e explicação geral sobre o projeto e a aplicação (o mesmo arquivo que você está lendo agora).

## 💻 Configuração para desenvolvimento e execução do código

### Pré-requisitos

Antes de iniciar, verifique se você tem instalado:

- [Node.js](https://nodejs.org/) (versão 16+ recomendada)
- [npm](https://www.npmjs.com/) ou [yarn](https://yarnpkg.com/)
- Banco de dados compatível com Sequelize (ex: PostgreSQL, MySQL ou SQLite)
- [Git](https://git-scm.com/)

### Como executar o projeto localmente

1. **Clone o repositório:**

```bash
git clone https://github.com/seu-usuario/2025-1B-T19-IN02-G03-main.git
cd 2025-1B-T19-IN02-G03-main
```

2. **Instale as dependências:**

```bash
npm install
# ou
yarn install
```

3. **Configure o ambiente:**
Crie um arquivo .env na raiz do projeto com base no arquivo .env.example:

```bash
cp src/.env.example src/.env
```
Edite o .env com as informações do seu banco de dados e porta do servidor.


4. **Configure o banco de dados**

Se necessário, execute as migrations:
```bash
npx sequelize db:migrate
```

5. **Inicie o servidor:**
```bash
node src/server.js
```

6. **Acesse o sistema**
```bash
Abra o navegador e vá para: http://localhost:3000
(Ou para a porta definida no .env)
```


## 🗃 Histórico de lançamentos

* 0.5.0 - 25/06/2024
    * Desenvolvimento da versão final do sistema web
    * Relatório de testes de usabilidade
* 0.4.0 - 13/06/2024
    * Desenvolvimento da 2º versão do sistema web
    * Relatório de Testes automatizados
    * Estudo de mercado e plano de marketing
* 0.3.0 - 30/05/2024
    * Desenvolvimento do Protótipo de Alta Fidelidade
    * Desenvolvimento da arquitetura da solução
    * Criação da 1º versão web
* 0.2.0 - 16/05/2024
    * Desenvolvimento do WireFrame
    * Modelagem do Banco de Dados
    * Consultas SQL
* 0.1.0 - 02/05/2024
   * Entendimento do negócio e do usuário

## 📋 Licença/License


<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/cc.svg?ref=chooser-v1">
<img style="height:22px!important;margin-left:3px;vertical-align:text-bottom;" src="https://mirrors.creativecommons.org/presskit/icons/by.svg?ref=chooser-v1">
<p xmlns:cc="http://creativecommons.org/ns#" xmlns:dct="http://purl.org/dc/terms/">
  <a property="dct:title" rel="cc:attributionURL" href="https://github.com/Intelihub/Template_M2/">Conferidas+</a> 
  by 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/livianegrini/">Lívia Negrini</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/bruno-frossard">Bruno Frossard</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/maurihkorn1818/">Mauri Herman Korn</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/ariel-mouadeb-71686624a/">Ariel Natan Mouadeb</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/nathaliafigueredo">Nathália Pires de Figueredo</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/rafael-figueiredo-campos-72131735b/">Rafael Campos</a>, 
  <a rel="cc:attributionURL dct:creator" property="cc:attributionName" href="https://www.linkedin.com/in/wendel-feitosa-975bb1346/">Wendel Feitosa</a> 
  is licensed under 
  <a href="http://creativecommons.org/licenses/by/4.0/?ref=chooser-v1" target="_blank" rel="license noopener noreferrer" style="display:inline-block;">Attribution 4.0 International</a>.
</p>


