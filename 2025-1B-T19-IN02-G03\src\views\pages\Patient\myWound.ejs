<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Formulário de Ferida</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C 0%, #5DB85A 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .title-section h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .content {
            padding: 30px 20px;
            background: white;
        }

        .content-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-secondary {
            background-color: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
        }

        .btn-secondary:hover {
            background-color: rgba(255,255,255,0.3);
        }

        .btn-success {
            background-color: #0D5F0C;
            color: white;
        }

        .btn-success:hover {
            background-color: #094509;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #0D5F0C, #3D963C);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        /* Form Styles */
        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e0e0e0;
            border-radius: 6px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3D963C;
        }

        .form-textarea {
            min-height: 120px;
            resize: vertical;
        }

        .wound-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3D963C;
        }

        .info-card h4 {
            color: #0D5F0C;
            margin-bottom: 10px;
        }

        .info-item {
            margin-bottom: 5px;
        }

        .info-label {
            font-weight: 500;
            color: #666;
        }

        .image-placeholder {
            background: #f0f0f0;
            border-radius: 8px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title-section h2 {
                font-size: 20px;
            }
            
            .wound-info {
                grid-template-columns: 1fr;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .card {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="logout-btn" onclick="goBack()">VOLTAR</button>
            <h1>Conferidas+</h1>

        </div>

        <div class="title-section">
            <h2>Formulário de Ferida</h2>
        </div>
        
        <div class="content">
            <div class="content-container">
                <div class="card">
                    <div class="card-header">
                        Ferida
                    </div>
                    <div class="card-body">
                        <div class="form-group">
                            <span class="form-label">Data de envio: 18/05/2025</span>
                        </div>
                        
                        <div class="wound-info">
                            <div class="info-card">
                                <h4>Informações da ferida:</h4>
                                <% if (wound) { %>
                                    <div class="info-item">
                                        <span class="info-label">Nível de dor:</span>
                                        <span class="pain-level <%= wound.pain_level <= 3 ? 'low' : wound.pain_level <= 6 ? 'medium' : 'high' %>">
                                            <%= wound.pain_level %>/10
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Coceira:</span>
                                        <span class="info-value"><%= wound.itch ? 'Sim' : 'Não' %></span>
                                    </div>
                                    <div class="info-item">
                                        <span class="info-label">Localização:</span>
                                        <span class="info-value"><%= wound.location || 'Não especificado' %></span>
                                    </div>
                                    <% if (wound.description) { %>
                                        <div class="info-item">
                                            <span class="info-label">Descrição:</span>
                                            <div class="info-value" style="margin-top: 5px;"><%= wound.description %></div>
                                        </div>
                                    <% } %>
                                </div>

                                <% } else { %>
                                    <div class="info-item">
                                        <span class="info-label">Ferida não encontrada.</span>
                                    </div>
                                <% } %>
                            </div>
                          <div class="info-card">
                                    <h4>Imagem da Ferida</h4>
                                    <div class="image-container">
                                        <% if (media && media.length > 0) { %>
                                            <img src="<%= media[0].path.startsWith('/') ? media[0].path : '/' + media[0].path %>" alt="Imagem da ferida" class="wound-image">
                                        <% } else { %>
                                            <div class="image-placeholder">📷<br>Nenhuma imagem disponível</div>
                                        <% } %>
                                    </div>
                                    <% if (media && media.length > 1) { %>
                                        <div style="margin-top: 10px; font-size: 0.9rem; color: #666;">
                                            + <%= media.length - 1 %> imagem(ns) adicional(is)
                                        </div>
                                    <% } %>
                                </div>
                        </div>
                        
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function saveWound() {
            const observations = document.querySelector('.form-textarea').value;
            

            console.log('Salvando ferida...');
            console.log('Observações:', observations);
            
            const saveBtn = document.querySelector('.btn-success');
            const originalText = saveBtn.textContent;
            saveBtn.textContent = 'Salvando...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.textContent = 'Salvo ✓';
                setTimeout(() => {
                    saveBtn.textContent = originalText;
                    saveBtn.disabled = false;
                }, 1000);
            }, 1000);
        }
    </script>
</body>
</html>