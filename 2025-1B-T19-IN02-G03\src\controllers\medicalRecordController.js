// controllers/medicalRecordController.js
const medicalRecordService = require("../services/medicalRecordService");

async function createMedicalRecord(req, res) {
  try {
    const medicalRecord = await medicalRecordService.createMedicalRecord(
      req.body
    );
    res.status(201).json(medicalRecord);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
}

async function getMedicalRecords(req, res) {
  try {
    const medicalRecords = await medicalRecordService.getMedicalRecords();
    res.status(200).json(medicalRecords);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}

async function getMedicalRecordById(req, res) {
  try {
    const medicalRecord = await medicalRecordService.getMedicalRecordById(
      req.params.id
    );
    res.status(200).json(medicalRecord);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function updateMedicalRecord(req, res) {
  try {
    const medicalRecord = await medicalRecordService.updateMedicalRecord(
      req.params.id,
      req.body
    );
    res.status(200).json(medicalRecord);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function deleteMedicalRecord(req, res) {
  try {
    await medicalRecordService.deleteMedicalRecord(req.params.id);
    res.status(200).json({ message: "Prontuario excluido com sucesso" });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  createMedicalRecord,
  getMedicalRecords,
  getMedicalRecordById,
  updateMedicalRecord,
  deleteMedicalRecord,
};
