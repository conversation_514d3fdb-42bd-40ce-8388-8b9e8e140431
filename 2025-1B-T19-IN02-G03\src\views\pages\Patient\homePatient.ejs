<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conferidas - Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .back-btn::before {
            content: "🏠";
            font-size: 16px;
        }

        .welcome-section {
            background: linear-gradient(135deg, #3D963C, #4a7c59);
            padding: 12px 20px;
            text-align: center;
        }

        .welcome-title {
            color: white;
            font-size: 36px;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .search-question {
            color: white;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }

        .menu-item {
            background-color: #0D5F0C;
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            color: white;
            text-decoration: none;
            transition: transform 0.2s, box-shadow 0.2s;
            min-height: 150px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .menu-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .menu-item.emergency {
            background-color: #e8f5e8;
            color: #333;
            border: 2px solid #ddd;
        }

        .menu-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .menu-text {
            font-size: 14px;
            font-weight: bold;
            line-height: 1.3;
            text-transform: uppercase;
        }

        .emergency-icon {
            font-size: 48px;
            color: #ff4444;
            margin-bottom: 10px;
        }

        .emergency-title {
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .emergency-subtitle {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
        }

        /* Ícones personalizados usando símbolos */
        .icon-update::before { content: "📱"; }
        .icon-history::before { content: "📋"; }
        .icon-video::before { content: "▶️"; }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.3rem;
            }
            
            .welcome-title {
                font-size: 28px;
            }
            
            .search-question {
                font-size: 20px;
            }
            
            .menu-grid {
                padding: 15px;
                gap: 15px;
            }
            
            .menu-item {
                padding: 20px 15px;
                min-height: 120px;
            }
        }

        @media (max-width: 480px) {
            .welcome-title {
                font-size: 24px;
            }
            
            .search-question {
                font-size: 18px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goHome()"> SAIR </button>
            <h1>Conferidas+</h1>
        </div>

        <div class="welcome-section">
            <h1 class="welcome-title">Bem-vindo ao Portal de Monitoramento!</h1>
            <p class="search-question">O que procura?</p>
        </div>

        <div class="menu-grid">
            <a href="/patient/atualizar-ferida/<%= patient.id %>" class="menu-item">
                <div class="menu-icon icon-update"></div>
                <div class="menu-text">Atualize os<br>registros da sua<br>ferida</div>
            </a>

            <a href="/wound/patient/<%= patient.id %>/wounds" class="menu-item">
                <div class="menu-icon icon-history"></div>
                <div class="menu-text">Histórico das<br>suas feridas</div>
            </a>

            <a href="/videos/list" class="menu-item">
                <div class="menu-icon icon-video"></div>
                <div class="menu-text">Vídeos<br>informativos</div>
            </a>

            <a href="/emergencia" class="menu-item emergency">
                <div class="emergency-icon">⚠️</div>
                <div class="emergency-title">URGÊNCIA?</div>
                <div class="emergency-subtitle">Clique aqui se<br>precisa de<br>suporte</div>
            </a>
        </div>
    </div>

    <script>
        function goHome() {
            window.location.href = '/';
        }

        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', function(e) {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>