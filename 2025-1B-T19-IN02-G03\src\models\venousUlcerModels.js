// models/venousUlcerModel.js
const Joi = require('joi');

const venousUlcerSchema = Joi.object({
  id: Joi.number().integer().positive().optional(),
  has_exudate: Joi.boolean().required(),
  description: Joi.string().max(255).required(),
  intensity: Joi.string().max(50).required(),
  odor: Joi.boolean().required(),
  odor_intensity: Joi.string().max(50).required(),
  skin_appearance: Joi.string().max(255).required(),
  skin_temperature: Joi.string().max(50).required(),
  wound_edematous: Joi.boolean().required(),
  edema_intensity: Joi.string().max(50).required(),
  pain: Joi.boolean().required(),
  pain_intensity: Joi.number().integer().required(),
  pain_frequency: Joi.string().max(50).required(),
  medical_record_id: Joi.number().integer().positive().required()
});

module.exports = venousUlcerSchema;