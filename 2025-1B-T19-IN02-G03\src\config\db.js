const path = require("path");
const { Pool } = require("pg");
require("dotenv").config();

const pool = new Pool({
  host: process.env.DB_HOST,
  port: process.env.DB_PORT,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
});

// Exporta o pool como padrão
module.exports = pool;

// Ordena os arquivos por nome (timestamp)
const migrationsDir = path.join(__dirname, "./migrations/production/202505290840fisico.sql");
exports.migrationsDir = migrationsDir;