<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Como cuidar da sua ferida - Conferidas</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }

        .header {
            background: linear-gradient(135deg, #4a7c59, #6b8e23);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .back-button {
            background: none;
            border: none;
            color: white;
            font-size: 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .back-button:hover {
            opacity: 0.8;
        }

        .logo {
            font-weight: bold;
            font-size: 18px;
        }

        .page-title {
            background-color: #a8c69f;
            color: #2d5016;
            padding: 20px;
            font-size: 28px;
            font-weight: bold;
            text-align: left;
            line-height: 1.2;
        }

        .content {
            padding: 30px 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .media-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }

        .media-thumbnail {
            width: 100%;
            max-width: 600px;
            height: 300px;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 0 auto 20px;
            display: block;
            background-color: #f8f8f8;
        }

        .description-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .description-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .description-text {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
            text-align: justify;
        }

        .bottom-bar {
            background: linear-gradient(135deg, #4a7c59, #6b8e23);
            height: 60px;
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
        }

        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .media-thumbnail {
                height: 200px;
            }
            
            .page-title {
                font-size: 24px;
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <button class="back-button" onclick="history.back()">
            <span>←</span>
            <span>VOLTAR</span>
        </button>
        <div class="logo">Conferidas</div>
    </header>

    <div class="page-title">
        Como cuidar da sua ferida
    </div>

    <main class="content">
        <div class="media-container">
            <div class="media-thumbnail"></div>
        </div>

        <div class="description-section">
            <h2 class="description-title">Descrição:</h2>
            <p class="description-text">
                Yorem ipsum dolor sit amet, consectetur adipiscing elit. Nunc vulputate, metus in 
                vaculis tincidunt, turpis purus ylobortis nisl, vel aliquet yypsum nulla id justo.
            </p>
        </div>
    </main>

    <div class="bottom-bar"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Página de mídia carregada');
        });
    </script>
</body>
</html>