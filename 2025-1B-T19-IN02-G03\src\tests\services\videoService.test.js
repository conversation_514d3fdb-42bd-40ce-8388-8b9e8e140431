const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  updateVideo: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/videoRepository', () => mockRepository);

const videoService = require('../../services/videoService');

describe('videoService', () => {
  const videoValido = {
    title: 'Título do vídeo',
    description: 'Descrição do vídeo',
    external_link: 'https://example.com/video',
    patient_id: 5
  };

  const videoInvalido = {
    id: 1,
    title: '',
    description: '',
    external_link: '',
    patient_id: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createVideo', () => {
    it('deve criar vídeo com dados válidos', async () => {
      const respostaMock = { id: 1, ...videoValido };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await videoService.createVideo(videoValido);

      expect(mockRepository.create).toHaveBeenCalledWith(videoValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se os dados forem inválidos (incluindo id)', async () => {
      await expect(videoService.createVideo(videoInvalido))
        .rejects.toThrow('Id, título, descrição, link externo e ID do paciente são obrigatórios.');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getVideo', () => {
    it('deve retornar todos os vídeos', () => {
      videoService.getVideo();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getVideoById', () => {
    it('deve retornar vídeo se encontrado', async () => {
      const respostaMock = { id: 1, ...videoValido };
      mockRepository.findById.mockResolvedValue(respostaMock);

      const resultado = await videoService.getVideoById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se vídeo não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(videoService.getVideoById(999))
        .rejects.toThrow('Vídeo não encontrado.');
    });
  });

  describe('updateVideo', () => {
    it('deve atualizar vídeo com dados válidos', async () => {
      const respostaMock = { id: 1, ...videoValido };
      mockRepository.updateVideo.mockResolvedValue(respostaMock);

      const resultado = await videoService.updateVideo(1, videoValido);

      expect(mockRepository.updateVideo).toHaveBeenCalledWith(1, videoValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se vídeo não for encontrado', async () => {
      mockRepository.updateVideo.mockResolvedValue(null);

      await expect(videoService.updateVideo(999, videoValido))
        .rejects.toThrow('Vídeo não encontrado.');
    });

    it('deve lançar erro se dados forem inválidos (com id incluso)', async () => {
      await expect(videoService.updateVideo(1, videoInvalido))
        .rejects.toThrow('Id, título, descrição, link externo e ID do paciente são obrigatórios.');
    });
  });

  describe('deleteVideo', () => {
    it('deve deletar vídeo se encontrado', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await videoService.deleteVideo(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se vídeo não for encontrado para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(videoService.deleteVideo(1))
        .rejects.toThrow('Vídeo não encontrado.');
    });
  });
});
