<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sistema de Gerenciamento de Pacientes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C 0%, #5DB85A 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .title-section h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .content {
            padding: 30px 20px;
            background: white;
        }

        .content-container {
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Cards */
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .card-header {
            background: linear-gradient(135deg, #0D5F0C, #3D963C);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .card-body {
            padding: 20px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #3D963C;
            color: white;
            margin-bottom: 20px;
        }

        .btn-primary:hover {
            background-color: #2d7a2b;
            transform: translateY(-1px);
        }

        .btn-danger {
            background-color: #2d7a2b;
            color: white;
            font-size: 12px;
            padding: 6px 10px;
        }

        .btn-danger:hover {
            background-color: #3D963C;
            transform: translateY(-1px);
        }

        .search-container {
            position: relative;
            margin-bottom: 20px;
        }

        .search-input {
            width: 100%;
            padding: 12px 40px 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.2s;
        }

        .search-input:focus {
            outline: none;
            border-color: #3D963C;
        }

        .search-icon {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            color: #666;
        }

        .patient-list {
            display: grid;
            gap: 12px;
        }

        .patient-item {
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 4px solid #3D963C;
        }

        .patient-item:hover {
            background: #e8f5e8;
            transform: translateX(5px);
        }

        .patient-item.active {
            background: #3D963C;
            color: white;
        }

        .history-list {
            display: grid;
            gap: 10px;
        }

        .history-item {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
            transition: all 0.2s;
            text-decoration: none;
            color: #333;
        }

        .history-item:hover {
            background: #e8f5e8;
            color: #333;
        }

        .history-date {
            font-weight: 500;
            color: #0D5F0C;
            flex: 1;
        }

        .patient-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #333;
        }

        .placeholder {
            color: #666;
            font-style: italic;
        }

        .screen {
            display: none;
        }

        .screen.active {
            display: block;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            animation: fadeIn 0.3s ease-out;
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 90%;
            text-align: center;
            animation: slideIn 0.3s ease-out;
        }

        .modal-header {
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #2d7a2b;
            margin-bottom: 10px;
        }

        .modal-body {
            margin-bottom: 25px;
            color: #666;
            line-height: 1.5;
        }

        .modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .btn-confirm {
            background-color: #3D963C;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-confirm:hover {
            background-color: #2d7a2b;
        }

        .btn-cancel {
            background-color: #6c757d;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
        }

        .btn-cancel:hover {
            background-color: #5a6268;
        }
        

        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title-section h2 {
                font-size: 20px;
            }

            .history-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }

            .patient-actions {
                align-self: flex-end;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: scale(0.9) translateY(-20px); }
            to { opacity: 1; transform: scale(1) translateY(0); }
        }

        .card {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div id="screen-patients" class="screen active">
            <div class="header">
                <button class="logout-btn" onclick="window.location.href='/agent/registerAgent'">LOGOUT</button>
                <h1>Conferidas+</h1>
            </div>

            <div class="title-section">
                <h2>Sistema de Gerenciamento de Pacientes</h2>
            </div>
            
            <div class="content">
                <div class="content-container">
                    <div class="card">
                        <div class="card-header">
                            Meus Pacientes
                        </div>
                        <div class="card-body">
                            <div class="search-container">
                                <input type="text" class="search-input" placeholder="Buscar pacientes...">
                                <span class="search-icon">🔍</span>
                            </div>

                            <a href="/agent/formPatient" class="btn btn-primary" style="width: auto; margin-top: 20px;">
                                ➕ Novo Paciente
                            </a>

                            
                            <div class="card">
                                <div class="card-body">
                                    <div class="section-title">
                                        Pacientes cadastrados:
                                    </div>
                                    <div class="history-list">
                                    <% if (Array.isArray(patients) && patients.length > 0) { %>
                                        <% patients.forEach(function(patient) { %>
                                            <div class="history-item">
                                                <a class="history-date" href="/agent/patient/<%= patient.id %>" style="text-decoration: none; color: inherit;">
                                                    <%= patient.name %>  <%= patient.dataNascimento ? patient.dataNascimento : '' %>
                                                </a>
                                                <div class="patient-actions">
                                                    <button class="btn btn-danger" onclick="confirmDelete('<%= patient.id %>', '<%= patient.name %>')">
                                                        🗑️
                                                    </button>
                                                </div>
                                            </div>
                                        <% }) %>
                                    <% } else { %>
                                        <div class="history-item placeholder">
                                            <div class="history-date">
                                                Nenhum paciente cadastrado
                                            </div>
                                        </div>
                                    <% } %>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="deleteModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">⚠️ Confirmar Exclusão</div>
            </div>
            <div class="modal-body">
                <p>Tem certeza que deseja deletar o paciente <strong id="patientName"></strong>?</p>
                <p>Esta ação não pode ser desfeita.</p>
            </div>
            <div class="modal-actions">
                <button class="btn-confirm" onclick="deletePatient()">Sim, Deletar</button>
                <button class="btn-cancel" onclick="closeModal()">Cancelar</button>
            </div>
        </div>
    </div>

    <script>
        let patientToDelete = null;

        function confirmDelete(patientId, patientName) {
            patientToDelete = patientId;
            document.getElementById('patientName').textContent = patientName;
            document.getElementById('deleteModal').classList.add('show');
        }

        function closeModal() {
            document.getElementById('deleteModal').classList.remove('show');
            patientToDelete = null;
        }

        function deletePatient() {
            if (patientToDelete) {
                fetch(`/patient/${patientToDelete}`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                })
                .then(response => {
                    if (response.ok) {
                        window.location.reload();
                    } else {
                        alert('Erro ao deletar paciente. Tente novamente.');
                    }
                })
                .catch(error => {
                    console.error('Erro:', error);
                    alert('Erro ao deletar paciente. Tente novamente.');
                });
            }
            closeModal();
        }

        document.querySelector('.search-input').addEventListener('input', function(e) {
            const searchTerm = e.target.value.toLowerCase();
            const patientItems = document.querySelectorAll('.history-item:not(.placeholder)');
            
            patientItems.forEach(item => {
                const patientName = item.querySelector('.history-date').textContent.toLowerCase();
                if (patientName.includes(searchTerm)) {
                    item.style.display = 'flex';
                } else {
                    item.style.display = 'none';
                }
            });
        });

        document.getElementById('deleteModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>