/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Paleta de cores hospitalares */
  --primary-green: #00bf24;
  --secondary-green: #56ae20;
  --light-green: #e8f5e9;
  --dark-green: #005713;
  --white: #ffffff;
  --light-gray: #f5f5f5;
  --medium-gray: #e0e0e0;
  --dark-gray: #616161;
  --text-dark: #333333;
  --text-medium: #555555;
  --shadow: rgba(0, 0, 0, 0.1);
}

body {
  font-family: 'Montserrat', 'DM Sans', sans-serif;
  background-color: var(--light-gray);
  color: var(--text-dark);
  line-height: 1.6;
}

/* Estilos da página inicial */
.landing-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--white);
}

.landing-header {
  background-color: var(--white);
  padding: 1.5rem;
  box-shadow: 0 2px 10px var(--shadow);
  border-bottom: 3px solid var(--primary-green);
}

.logo-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 3rem;
}

.logo {
  height: 70px;
}

.landing-main {
  flex: 1;
  padding: 3rem 2rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.landing-intro {
  text-align: center;
  margin-bottom: 4rem;
  padding: 3rem;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow);
  border-top: 5px solid var(--primary-green);
}

.landing-intro h1 {
  color: var(--primary-green);
  margin-bottom: 1.5rem;
  font-size: 2.8rem;
  font-weight: 700;
}

.landing-intro p {
  margin-bottom: 1.2rem;
  font-size: 1.2rem;
  color: var(--text-medium);
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.landing-options h2 {
  text-align: center;
  color: var(--dark-green);
  margin-bottom: 2.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.options-container {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.option-card {
  background-color: var(--white);
  padding: 2.5rem;
  border-radius: 12px;
  box-shadow: 0 4px 20px var(--shadow);
  text-align: center;
  flex: 1;
  min-width: 320px;
  max-width: 420px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.option-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 5px;
  background-color: var(--primary-green);
}

.option-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.option-card h3 {
  color: var(--primary-green);
  margin-bottom: 1.2rem;
  font-size: 1.8rem;
  font-weight: 600;
}

.option-card p {
  margin-bottom: 2rem;
  color: var(--text-medium);
  font-size: 1.1rem;
}

.option-button {
  display: inline-block;
  background-color: var(--primary-green);
  color: var(--white);
  padding: 1rem 2rem;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  box-shadow: 0 4px 10px rgba(0, 191, 36, 0.3);
}

.option-button:hover {
  background-color: var(--dark-green);
  box-shadow: 0 6px 15px rgba(0, 191, 36, 0.4);
}

.landing-footer {
  background-color: var(--dark-gray);
  color: var(--white);
  text-align: center;
  padding: 1.8rem;
  margin-top: 3rem;
  font-size: 0.95rem;
}

/* Estilos para as páginas de login */
.login-container {
  max-width: 480px;
  margin: 6% auto;
  background-color: var(--white);
  border-radius: 12px;
  box-shadow: 0 8px 30px var(--shadow);
  padding: 2.5rem;
  border-top: 5px solid var(--primary-green);
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-logo {
  height: 90px;
  margin-bottom: 1.5rem;
}

.login-header h1 {
  color: var(--primary-green);
  font-size: 2rem;
  font-weight: 600;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.8rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

.form-group label {
  font-weight: 600;
  color: var(--text-medium);
  font-size: 1.05rem;
}

.form-group input {
  padding: 1rem;
  border: 2px solid var(--medium-gray);
  border-radius: 8px;
  font-size: 1.05rem;
  transition: border-color 0.3s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--primary-green);
}

.login-button {
  background-color: var(--primary-green);
  color: var(--white);
  border: none;
  padding: 1.2rem;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 1rem;
  box-shadow: 0 4px 10px rgba(0, 191, 36, 0.3);
}

.login-button:hover {
  background-color: var(--dark-green);
  box-shadow: 0 6px 15px rgba(0, 191, 36, 0.4);
}

.login-footer {
  text-align: center;
  margin-top: 2.5rem;
}

.back-link {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  transition: color 0.3s ease;
}

.back-link:before {
  content: '←';
  margin-right: 0.5rem;
}

.back-link:hover {
  color: var(--dark-green);
}

/* Responsividade */
@media (max-width: 768px) {
  .landing-intro {
    padding: 2rem;
  }
  
  .landing-intro h1 {
    font-size: 2.2rem;
  }
  
  .option-card {
    min-width: 100%;
  }
  
  .login-container {
    margin: 5% 1rem;
    padding: 2rem;
  }
}