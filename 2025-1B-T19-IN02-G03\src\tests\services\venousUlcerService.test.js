const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/venousUlcerRepository', () => mockRepository);

const venousUlcerService = require('../../services/venousUlcerService');

describe('venousUlcerService', () => {
  const ulceraValida = {
    description: 'Ferida com exsudato moderado',
    intensity: 'moderada',
  };

  const ulceraInvalida = {
    description: '',
    intensity: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createVenousUlcer', () => {
    it('deve criar úlcera venosa com dados válidos', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await venousUlcerService.createVenousUlcer(ulceraValida);

      expect(mockRepository.create).toHaveBeenCalledWith(ulceraValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se os campos obrigatórios estiverem ausentes', async () => {
      await expect(venousUlcerService.createVenousUlcer(ulceraInvalida))
        .rejects.toThrow('Descrição e intensidade são obrigatórias.');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getVenousUlcers', () => {
    it('deve retornar todas as úlceras venosas', () => {
      venousUlcerService.getVenousUlcers();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getVenousUlcerById', () => {
    it('deve retornar úlcera venosa por ID se existir', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.findById.mockResolvedValue(respostaMock);

      const resultado = await venousUlcerService.getVenousUlcerById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se úlcera venosa não for encontrada', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(venousUlcerService.getVenousUlcerById(999))
        .rejects.toThrow('Úlcera venosa não encontrada');
    });
  });

  describe('updateVenousUlcer', () => {
    it('deve atualizar úlcera venosa com dados válidos', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.update.mockResolvedValue(respostaMock);

      const resultado = await venousUlcerService.updateVenousUlcer(1, ulceraValida);

      expect(mockRepository.update).toHaveBeenCalledWith(1, ulceraValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se úlcera venosa não for encontrada', async () => {
      mockRepository.update.mockResolvedValue(null);

      await expect(venousUlcerService.updateVenousUlcer(999, ulceraValida))
        .rejects.toThrow('Úlcera venosa não encontrada');
    });

    it('deve lançar erro se os dados forem inválidos', async () => {
      await expect(venousUlcerService.updateVenousUlcer(1, ulceraInvalida))
        .rejects.toThrow('Descrição e intensidade são obrigatórias.');
    });
  });

  describe('deleteVenousUlcer', () => {
    it('deve deletar úlcera venosa se encontrada', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await venousUlcerService.deleteVenousUlcer(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se úlcera venosa não for encontrada para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(venousUlcerService.deleteVenousUlcer(1))
        .rejects.toThrow('Úlcera venosa não encontrada');
    });
  });
});
