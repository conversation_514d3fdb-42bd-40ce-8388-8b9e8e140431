CREATE TABLE IF NOT EXISTS agent (
  id SERIAL PRIMARY KEY,
  name VA<PERSON>HAR(100) NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  crm VARCHAR(50) NOT NULL,
  email VARCHAR(250) NOT NULL,
  password VARCHAR(200) NOT NULL
);

CREATE TABLE IF NOT EXISTS patient (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  age INT NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  password VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id)
);

CREATE TABLE IF NOT EXISTS wound (
  id SERIAL PRIMARY KEY,
  pain_level VARCHAR(100) NOT NULL,
  description VARCHAR(255) NOT NULL,
  date DATE NOT NULL,
  itch BOOLEAN NOT NULL,
  agent_id INT NOT NULL,
    REFERENCES agent(id),
  patient_id INT NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS video (
  id SERIAL PRIMARY KEY,
  external_link VARCHAR(255) NOT NULL,
  description VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  patient_id INT NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS medical_record (
  id SERIAL PRIMARY KEY,
  wound_etiology VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  width VARCHAR(50) NOT NULL,
  length VARCHAR(50) NOT NULL,
  depth VARCHAR(50) NOT NULL,
  wound_bed VARCHAR(255) NOT NULL,
  wound_edge VARCHAR(255) NOT NULL,
  exudate VARCHAR(255) NOT NULL,
  exudate_characteristics VARCHAR(255) NOT NULL,
  exudate_intensity VARCHAR(255) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(255),
  appearance VARCHAR(255) NOT NULL,
  temperature VARCHAR(50) NOT NULL,
  edema BOOLEAN NOT NULL,
  edema_intensity VARCHAR(255),
  pain BOOLEAN NOT NULL,
  pain_intensity VARCHAR(255),
  pain_frequency VARCHAR(255) NOT NULL,
  risk_factors VARCHAR(255) NOT NULL,
  risk_specifications VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  patient_id INT UNIQUE NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS agent_medical_record (
  id SERIAL PRIMARY KEY,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS media (
  id SERIAL PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  path VARCHAR(255) NOT NULL,
  wound_id INT NOT NULL
    REFERENCES wound(id)
);

CREATE TABLE IF NOT EXISTS pressure_ulcer (
  id SERIAL PRIMARY KEY,
  grade VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS venous_ulcer (
  id SERIAL PRIMARY KEY,
  has_exudate BOOLEAN NOT NULL,
  description VARCHAR(255) NOT NULL,
  intensity VARCHAR(50) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(50) NOT NULL,
  skin_appearance VARCHAR(255) NOT NULL,
  skin_temperature VARCHAR(50) NOT NULL,
  wound_edematous BOOLEAN NOT NULL,
  edema_intensity VARCHAR(50) NOT NULL,
  pain BOOLEAN NOT NULL,
  pain_intensity INT NOT NULL,
  pain_frequency VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS diabetic_foot_ulcer (
  id SERIAL PRIMARY KEY,
  care VARCHAR(255) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS foot_physical_exam (
  id SERIAL PRIMARY KEY,
  patient_id INT NOT NULL,
  exam_date DATE NOT NULL,
  abnormal_toe_or_foot_shape BOOLEAN NOT NULL,
  callus BOOLEAN NOT NULL,
  mycosis BOOLEAN NOT NULL,
  ingrown_nails BOOLEAN NOT NULL,
  straight_nail_cut BOOLEAN NOT NULL,
  curved_nail_cut BOOLEAN NOT NULL,
  poor_foot_hygiene BOOLEAN NOT NULL,
  wears_synthetic_sock_or_none BOOLEAN NOT NULL,
  wears_inappropriate_footwear BOOLEAN NOT NULL,
  muscle_weakness BOOLEAN NOT NULL,
  macerated_or_dry_skin BOOLEAN NOT NULL,
  interdigital_fissure BOOLEAN NOT NULL,
  amputation BOOLEAN NOT NULL,
  hallux_valgus BOOLEAN NOT NULL,
  edema BOOLEAN NOT NULL,
  muscle_weakness_notes VARCHAR(255) NOT NULL,
  amputation_location VARCHAR(255),
  diabetic_foot_ulcer_id INT NOT NULL
    REFERENCES diabetic_foot_ulcer(id)
);