const woundRepository = require('../repositories/woundRepository');
const woundSchema = require('../models/woundModel');
const mediaService = require('./mediaService');


function validate(data) {
  const { error } = woundSchema.validate(data);
  if (error) throw new Error(error.details[0].message);
}

async function createWound(data) {
  validate(data);
  return await woundRepository.create(data);
}

function getWounds() {
  return woundRepository.getAll();
}

async function getWoundById(id) {
  const wound = await woundRepository.findById(id);
  if (!wound) throw new Error('Wound not found');

  const media = await mediaService.getMediaByWoundId(id);
  return { wound, media: media || [] };
}

async function getWoundsByPatientId(id) {
  return await woundRepository.findAllByPatientId(Number(id));
}

async function updateWound(id, data) {
  validate(data);
  const updated = await woundRepository.updateWound(id, data);
  if (!updated) throw new Error('Wound not found');
  return updated;
}

async function deleteWound(id) {
  const deleted = await woundRepository.delete(id);
  if (!deleted) throw new Error('Wound not found');
  return true;
}

module.exports = {
  createWound,
  getWounds,
  getWoundById,
  getWoundsByPatientId,
  updateWound,
  deleteWound
};
