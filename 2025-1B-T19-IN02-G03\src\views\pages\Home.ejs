<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Telemonitoramento de Feridas - FMUSP</title>
  <style>
    :root {
      --primary-green: #246824;
      --dark-green: #246824;
      --light-green: #e8f5e9;
      --white: #ffffff;
      --text-dark: #333333;
      --text-medium: #666666;
      --text-light: #999999;
      --shadow: rgba(0, 0, 0, 0.1);
    }
    
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Montserrat', 'DM Sans', sans-serif;
      color: var(--text-dark);
      background-color: #f9f9f9;
      line-height: 1.6;
    }
    
    .landing-container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .landing-header {
      padding: 1.5rem 0;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .logo-container {
      display: flex;
      align-items: center;
      gap: 2rem;
    }
    
    .logo {
      height: 80px;
    }
    
    .landing-main {
      padding: 2rem 0;
    }
    
    .landing-intro {
      background: linear-gradient(135deg, var(--primary-green), var(--dark-green));
      color: var(--white);
      padding: 3rem;
      border-radius: 12px;
      margin-bottom: 3rem;
      box-shadow: 0 8px 30px var(--shadow);
    }
    
    .landing-intro h1 {
      font-size: 2.8rem;
      margin-bottom: 1.5rem;
      font-weight: 700;
    }
    
    .landing-intro p {
      font-size: 1.1rem;
      max-width: 800px;
      margin-bottom: 1rem;
    }
    
    .landing-options h2 {
      text-align: center;
      font-size: 2rem;
      margin-bottom: 2rem;
      color: var(--primary-green);
    }
    
    .options-container {
      display: flex;
      gap: 2rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .option-card {
      background-color: var(--white);
      border-radius: 12px;
      padding: 2.5rem;
      box-shadow: 0 8px 30px var(--shadow);
      flex: 1;
      min-width: 300px;
      max-width: 500px;
      display: flex;
      flex-direction: column;
    }
    
    .option-card h3 {
      font-size: 1.8rem;
      margin-bottom: 1rem;
      color: var(--primary-green);
    }
    
    .option-card p {
      margin-bottom: 2rem;
      flex-grow: 1;
    }
    
    .option-button {
      display: inline-block;
      background-color: var(--primary-green);
      color: var(--white);
      padding: 1rem 2rem;
      border-radius: 50px;
      text-decoration: none;
      font-weight: 600;
      font-size: 1.1rem;
      text-align: center;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(0, 191, 36, 0.3);
    }
    
    .option-button:hover {
      background-color: var(--dark-green);
      box-shadow: 0 6px 15px rgba(0, 191, 36, 0.4);
    }
    
    .landing-footer {
      background-color: var(--text-dark);
      color: var(--white);
      text-align: center;
      padding: 1.8rem;
      margin-top: 3rem;
      font-size: 0.95rem;
      border-radius: 12px;
    }
    
    @media (max-width: 768px) {
      .landing-intro {
        padding: 2rem;
      }
      
      .landing-intro h1 {
        font-size: 2.2rem;
      }
      
      .option-card {
        min-width: 100%;
      }
    }
  </style>
</head>
<body>
  <div class="landing-container">
    <header class="landing-header">
    </header>

    <main class="landing-main">
      <section class="landing-intro">
        <h1>Telemonitoramento de Feridas</h1>
        <p>Bem-vindo ao sistema integrado de telemonitoramento de feridas da FMUSP em parceria com o Inteli.</p>
        <p>Nossa plataforma permite o acompanhamento remoto de pacientes, facilitando o diagnóstico, tratamento e recuperação através de tecnologia avançada e atendimento humanizado.</p>
      </section>

      <section class="landing-options">
        <h2>Escolha seu perfil de acesso</h2>
        <div class="options-container">
          <div class="option-card">
            <h3>Área do Paciente</h3>
            <p>Acesse para enviar informações sobre seu tratamento, acompanhar sua evolução e receber orientações personalizadas da equipe médica.</p>
            <a href="/patient/loginPatient" class="option-button">Entrar como Paciente</a>
          </div>

          <div class="option-card">
            <h3>Área do Profissional</h3>
            <p>Acesse para monitorar pacientes, analisar a evolução de tratamentos, gerenciar casos clínicos e fornecer orientações especializadas.</p>
            <a href="/agent/loginAgent" class="option-button">Entrar como Profissional</a>
          </div>
        </div>
      </section>
    </main>

    <footer class="landing-footer">
      <p>&copy; 2025 - Faculdade de Medicina da USP & Instituto de Tecnologia e Liderança - Todos os direitos reservados</p>
    </footer>
  </div>
</body>
</html>
