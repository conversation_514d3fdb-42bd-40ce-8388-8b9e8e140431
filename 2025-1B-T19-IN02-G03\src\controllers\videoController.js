// controllers/videoController.js
const videoService = require("../services/videoService");

async function createVideo(req, res) {
  try {
    const video = await videoService.createVideo(
      req.body
    );
    res.status(201).json(video);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
}

async function getVideo(req, res) {
  try {
    const videos = await videoService.getVideo();  
    res.render("pages/patient/listMidia", { videos });  
  } catch (err) {
    console.error("Erro completo:", err);
    res.status(500).send(err.message || "Erro desconhecido");
  }
}


async function getVideoById(req, res) {
  try {
    const video = await videoService.getVideoById(
      req.params.id
    );
    res.status(200).json(video);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function updateVideo(req, res) {
  try {
    const video = await videoService.updateVideo(
      req.params.id,
      req.body
    );
    res.status(200).json(video);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function deleteVideo(req, res) {
  try {
    await videoService.deleteVideo(req.params.id);
    res.status(200).json({ message: "Video excluido com sucesso" });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  createVideo,
  getVideo,
  getVideoById,
  updateVideo,
  deleteVideo,
}; 