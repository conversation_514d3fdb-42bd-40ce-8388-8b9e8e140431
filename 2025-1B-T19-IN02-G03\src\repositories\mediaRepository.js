const pool = require('../config/db.js');

async function create(media) {
  const query = `
    INSERT INTO media (type, path, wound_id)
    VALUES ($1, $2, $3)
    RETURNING *;
  `;

  const values = [media.type, media.path, media.wound_id];
  console.log('Executando query create media com values:', values);

  const result = await pool.query(query, values);
  return result.rows[0];
}

async function getAll() {
  const result = await pool.query(`SELECT * FROM media`);
  return result.rows;
}

async function findById(id) {
  const result = await pool.query(
    `SELECT * FROM media WHERE id = $1`,
    [id]
  );
  return result.rows[0];
}

async function findAllByWoundId(woundId) {
  const result = await pool.query(
    'SELECT * FROM media WHERE wound_id = $1',
    [Number(woundId)]
  );
  return result.rows;
}

async function updateMedia(id, data) {
  const { type, path, wound_id } = data;
  const result = await pool.query(
    `UPDATE media
     SET type = $1, path = $2, wound_id = $3
     WHERE id = $4 RETURNING *`,
    [type, path, wound_id, id]
  );
  return result.rows[0];
}

async function deleteMedia(id) {
  const result = await pool.query(
    `DELETE FROM media WHERE id = $1`,
    [id]
  );
  return result.rowCount > 0;
}

module.exports = {
  create,
  getAll,
  findById,
  findAllByWoundId,
  updateMedia,
  delete: deleteMedia
};
