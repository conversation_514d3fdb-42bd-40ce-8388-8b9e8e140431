const bcrypt = require('bcryptjs');

const mockRepository = {
  create: jest.fn(),
  findAll: jest.fn(),
  findById: jest.fn(),
  findByCpf: jest.fn(),
  update: jest.fn(),
  remove: jest.fn(),
};

jest.mock('../../repositories/patientRepository', () => mockRepository);

const mockValidate = jest.fn();
const mockDescribe = jest.fn(() => ({ keys: {} }));

// Mock da chain do Joi com fork() e min()
const mockFork = jest.fn(() => ({
  validate: mockValidate,
  describe: mockDescribe,
  min: jest.fn().mockReturnThis(), // permite encadear .min()
  messages: jest.fn().mockReturnThis(), // permite encadear .messages()
}));

jest.mock('../../models/patientModel', () => ({
  validate: mockValidate,
  describe: mockDescribe,
  fork: mockFork,
}));

const patientService = require('../../services/patientService');
const patientSchema = require('../../models/patientModel');

describe('patientService', () => {
  const pacienteValido = {
    nome: 'Carlos Teste',
    cpf: '12345678900',
    email: '<EMAIL>',
    password: 'senha123',
  };

  const pacienteComCPFExistente = { ...pacienteValido, id: 1 };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.spyOn(bcrypt, 'hash').mockResolvedValue('senha-hash');
    mockFork.mockClear();
    mockValidate.mockClear();
    mockDescribe.mockClear();
  });

  describe('createPatient', () => {
    it('deve criar paciente com dados válidos', async () => {
      mockValidate.mockReturnValue({ value: pacienteValido });
      mockRepository.findByCpf.mockResolvedValue(null);
      mockRepository.create.mockResolvedValue({ ...pacienteValido, password: 'senha-hash' });

      const result = await patientService.createPatient(pacienteValido);

      expect(result).not.toHaveProperty('password');
      expect(mockRepository.create).toHaveBeenCalled();
    });

    it('deve lançar erro se validação Joi falhar', async () => {
      mockValidate.mockReturnValue({
        error: { details: [{ message: '"cpf" é obrigatório' }] }
      });

      await expect(patientService.createPatient({}))
        .rejects.toThrow('Erro de validação: "cpf" é obrigatório');
    });

    it('deve lançar erro se CPF já existir', async () => {
      mockValidate.mockReturnValue({ value: pacienteValido });
      mockRepository.findByCpf.mockResolvedValue(pacienteComCPFExistente);

      await expect(patientService.createPatient(pacienteValido))
        .rejects.toThrow('CPF já cadastrado.');
    });
  });

  describe('getAllPatients', () => {
    it('deve retornar todos os pacientes omitindo senhas', async () => {
      const pacientes = [{ id: 1, ...pacienteValido, password: 'senha' }];
      mockRepository.findAll.mockResolvedValue(pacientes);

      const resultado = await patientService.getAllPatients();

      expect(resultado[0]).not.toHaveProperty('password');
    });
  });

  describe('getPatientById', () => {
    it('deve retornar paciente existente', async () => {
      const paciente = { id: 1, ...pacienteValido, password: 'senha' };
      mockRepository.findById.mockResolvedValue(paciente);

      const result = await patientService.getPatientById(1);

      expect(result).not.toHaveProperty('password');
    });

    it('deve lançar erro se paciente não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(patientService.getPatientById(999))
        .rejects.toThrow('Paciente não encontrado');
    });
  });

  describe('updatePatient', () => {
    it('deve atualizar paciente com campos válidos', async () => {
      const updateData = { email: '<EMAIL>' };
      const pacienteAntigo = { ...pacienteValido, id: 1 };
      const atualizado = { ...pacienteAntigo, ...updateData };

      mockRepository.findById.mockResolvedValue(pacienteAntigo);
      mockRepository.findByCpf.mockResolvedValue(null);
      mockFork.mockReturnValue({
        validate: jest.fn().mockReturnValue({ value: updateData }),
        describe: mockDescribe,
        min: jest.fn().mockReturnThis(),
        messages: jest.fn().mockReturnThis(),
      });
      mockRepository.update.mockResolvedValue(atualizado);

      const resultado = await patientService.updatePatient(1, updateData);

      expect(resultado).not.toHaveProperty('password');
      expect(resultado.email).toBe(updateData.email);
    });
  });

  describe('deletePatient', () => {
    it('deve deletar paciente existente', async () => {
      mockRepository.remove.mockResolvedValue(true);

      const resultado = await patientService.deletePatient(1);

      expect(resultado.message).toBe('Paciente deletado com sucesso.');
    });

    it('deve lançar erro se paciente não existir', async () => {
      mockRepository.remove.mockResolvedValue(false);

      await expect(patientService.deletePatient(1))
        .rejects.toThrow('Paciente não encontrado');
    });
  });
});
