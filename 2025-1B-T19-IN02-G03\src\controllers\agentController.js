const agentService = require("../services/agentService");
const patientService = require("../services/patientService");
const woundService = require("../services/woundService");
const mediaService = require("../services/mediaService");
const medicalRecordService = require("../services/medicalRecordService"); 


const homeAgent = async (req, res) => {
  try {
    const patients = await patientService.getAllPatients();
    res.render("pages/agent/homeAgent", { patients: patients || [] });
  } catch (err) {
    console.error("Erro ao carregar pacientes:", err);
    res.render("pages/agent/homeAgent", { patients: [] });
  }
};

const formPatient = async (req, res) => {
  try {
    res.render("pages/agent/formPatient");

  } catch (err) {
    console.error("Erro ao carregar formulário de paciente:", err);
    res.render("pages/agent/formPatient");
  }
};

const createPatient = async (req, res) => {
  try {

    const agentId = req.session.agentId;
    if (!req.session.agentId) {
      return res.render("pages/agent/formPatient", { 
        error: "Você precisa estar logado para criar um paciente",
        formData: req.body 
      });
    }

    const patientData = {
      name: req.body.name,
      phone: req.body.phone,
      age: req.body.age,
      cpf: req.body.cpf,
      password: req.body.password,
      agent_id: agentId,
    };

    const medicalRecordData = {
      wound_etiology: req.body.wound_etiology,    
      location: req.body.location,
      width: req.body.width,
      length: req.body.length,
      depth: req.body.depth,
      wound_bed: req.body.wound_bed,
      wound_edge: req.body.wound_edge,
      exudate: req.body.exudate,
      exudate_characteristics: req.body.exudate_characteristics,
      exudate_intensity: req.body.exudate_intensity,
      odor: req.body.odor,
      odor_intensity: req.body.odor_intensity,
      appearance: req.body.appearance,
      temperature: req.body.temperature,
      edema: req.body.edema,
      edema_intensity: req.body.edema_intensity,
      pain: req.body.pain,
      pain_intensity: req.body.pain_intensity,
      pain_frequency: req.body.pain_frequency,
      risk_factors: req.body.risk_factors,
      risk_specifications: req.body.risk_specifications,
      agent_id: agentId,
      patient_id: null, 
    };

  
    const patient = await patientService.createPatient(patientData);
    medicalRecordData.patient_id = patient.id;
    medicalRecordData.agent_id = agentId;


    const medicalRecord = await medicalRecordService.createMedicalRecord(medicalRecordData); ;


    res.redirect("/agent/homeAgent");

  } catch (err) {
    console.error("Erro ao criar paciente:", err);
    res.render("pages/agent/formPatient", { 
      error: err.message,
      formData: req.body 
    });
  }
};

const patientDetail = async (req, res) => {
  try {
    const patientId = req.params.id;
    const patient = await patientService.getPatientById(patientId);
    
    if (!patient) {
      console.log("Paciente não encontrado:", patientId);
      return res.status(404).render("pages/agent/patient", {
        patient: null,
        medicalRecord: null,
        woundUpdates: [],
        error: "Paciente não encontrado",
      });
    }

    let medicalRecord = null;
    try {
      medicalRecord = await medicalRecordService.getMedicalRecordByPatientId(patientId);
    } catch (medicalRecordError) {
      console.log("Prontuário médico não encontrado para o paciente:", patientId);
    }

    let woundUpdates = [];
    try {
      woundUpdates = await woundService.getWoundsByPatientId(patientId);

      woundUpdates = woundUpdates.slice().sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);
        return dateB - dateA;
      });

    } catch (woundError) {
      console.log("Erro ao buscar feridas do paciente:", woundError.message);
    }


    
    res.render("pages/agent/patient", { 
      patient, 
      medicalRecord, 
      woundUpdates: woundUpdates || [] 
    });

  } catch (err) {
    console.error("Erro geral ao buscar paciente:", err);
    res.status(500).render("pages/agent/patient", {
      patient: null,
      medicalRecord: null,
      woundUpdates: [],
      error: "Erro interno do servidor: " + err.message,
    });
  }
};

const woundDetail = async (req, res) => {
  try {
    const woundId = req.params.id;    
    const woundData = await woundService.getWoundById(woundId);
    const wound = woundData.wound;
    const media = woundData.media;
    
    if (!wound) {
      console.log("Ferida não encontrada, retornando 404");
      return res.status(404).render("pages/agent/wound", {
        wound: null,
        media: [],
        error: "Ferida não encontrada",
      });
    }
    

    const formattedMedia = Array.isArray(media) ? media : [];
    
    if (formattedMedia.length > 0) {
      formattedMedia.forEach((item, index) => {
        console.log(`Imagem ${index + 1}:`, item.path);
      });
    } else {
      console.log("Nenhuma imagem encontrada para esta ferida");
    }


    res.render("pages/agent/wound", { 
      wound, 
      media: formattedMedia,
      error: null 
    });
  } catch (err) {
    console.error("Erro geral ao buscar ferida:", err.message);
    console.error("Stack completo:", err.stack);
    res.status(500).render("pages/agent/wound", {
      wound: null,
      media: [],
      error: "Erro ao buscar ferida: " + err.message,
    });
  }
};

const loginAgent = (req, res) => {
  res.render("pages/agent/loginAgent", { error: null });
};

const processLogin = async (req, res) => {
  try {
    const { registro, senha } = req.body;

    if (!registro || !senha) {
      return res.status(400).render("pages/agent/loginAgent", {
        error: "Número de registro e senha são obrigatórios",
      });
    }

    const agent = await agentService.authenticateAgent(registro, senha);
    req.session.agentId = agent.id;
    res.redirect("/agent/homeAgent");
  } catch (err) {
    console.error("Erro no login:", err);
    res.render("pages/agent/loginAgent", {
      error: err.message,
    });
  }
};

const registerAgent = (req, res) => {
  res.render("pages/agent/registerAgent", { error: null });
};

const infoPatient = async (req, res) => {
  try {
    const patientId = req.params.id;
    const patient = await patientService.getPatientById(req.params.id);
    if (!patient) {
      return res.status(404).render("pages/agent/infoPatient", {
        patient: null,
        error: "Paciente não encontrado",
      });
    }

    let medicalRecord = null;
    try {
      medicalRecord = await medicalRecordService.getMedicalRecordByPatientId(patientId);
    } catch (medicalRecordError) {
    }

    res.render("pages/agent/infoPatient", { patient, medicalRecord, error: null });
  } catch (err) {
    res.status(500).render("pages/agent/infoPatient", {
      patient: null,
      
      error: "Erro ao buscar paciente",
    });
  }
};

async function createAgent(req, res) {
  try {
    const agent = await agentService.createAgent(req.body);
    res.redirect("/agent/loginAgent");
  } catch (err) {
    console.error("Erro ao criar agente:", err);
    res.render("pages/agent/registerAgent", {
      error: err.message,
    });
  }
}

async function getAgents(req, res) {
  try {
    const agents = await agentService.getAgents();
    res.status(200).json(agents);
  } catch (err) {
    console.error("Erro ao buscar agentes:", err);
    res.status(500).json({ error: err.message });
  }
}

async function getAgentById(req, res) {
  try {
    const agent = await agentService.getAgentById(req.params.id);
    res.status(200).json(agent);
  } catch (err) {
    console.error("Erro ao buscar agente:", err);
    res.status(404).json({ error: err.message });
  }
}

async function updateAgent(req, res) {
  try {
    const agent = await agentService.updateAgent(req.params.id, req.body);
    res.status(200).json(agent);
  } catch (err) {
    console.error("Erro ao atualizar agente:", err);
    res.status(404).json({ error: err.message });
  }
}

async function deleteAgent(req, res) {
  try {
    await agentService.deleteAgent(req.params.id);
    res.status(200).json({ message: "Agente excluído com sucesso" });
  } catch (err) {
    console.error("Erro ao deletar agente:", err);
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  homeAgent,
  formPatient,
  createPatient,
  patientDetail,
  woundDetail,
  infoPatient,
  loginAgent,
  processLogin,
  registerAgent,
  createAgent,
  getAgents,
  getAgentById,
  updateAgent,
  deleteAgent,
};