const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/agentRepository', () => mockRepository);
const agentService = require('../../services/agentService');


describe('agentService', () => {
  const agenteValido = {
    name: 'Dra. Ana',
    crm: '123456',
    email: '<EMAIL>',
    password: 'senhaSegura',
  };

  const agenteInvalido = {
    name: '',
    crm: '',
    email: '',
    password: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createAgent', () => {
    it('deve criar um agente válido', async () => {
      const respostaMock = { id: 1, ...agenteValido };

      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await agentService.createAgent(agenteValido);

      expect(mockRepository.create).toHaveBeenCalledTimes(1);
      expect(mockRepository.create).toHaveBeenCalledWith(agenteValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se dados obrigatórios estiverem faltando', async () => {
      await expect(agentService.createAgent(agenteInvalido))
        .rejects.toThrow('Nome, número de registro, email e senha são obrigatórios.');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getAgents', () => {
    it('deve retornar todos os agentes', () => {
      agentService.getAgents();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getAgentById', () => {
    it('deve retornar agente por ID se existir', async () => {
      const agente = { id: 1, ...agenteValido };
      mockRepository.findById.mockResolvedValue(agente);

      const resultado = await agentService.getAgentById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(agente);
    });

    it('deve lançar erro se agente não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(agentService.getAgentById(999))
        .rejects.toThrow('Agente não encontrado');
    });
  });

  describe('updateAgent', () => {
    it('deve atualizar agente com dados válidos', async () => {
      const atualizado = { id: 1, ...agenteValido };
      mockRepository.update.mockResolvedValue(atualizado);

      const resultado = await agentService.updateAgent(1, agenteValido);

      expect(mockRepository.update).toHaveBeenCalledWith(1, agenteValido);
      expect(resultado).toEqual(atualizado);
    });

    it('deve lançar erro se agente não for encontrado', async () => {
      mockRepository.update.mockResolvedValue(null);

      await expect(agentService.updateAgent(999, agenteValido))
        .rejects.toThrow('Agente não encontrado');
    });

    it('deve lançar erro se dados inválidos forem fornecidos', async () => {
      await expect(agentService.updateAgent(1, agenteInvalido))
        .rejects.toThrow('Nome, número de registro, email e senha são obrigatórios.');
    });
  });

  describe('deleteAgent', () => {
    it('deve deletar agente se existir', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await agentService.deleteAgent(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se agente não for encontrado para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(agentService.deleteAgent(1))
        .rejects.toThrow('Agente não encontrado');
    });
  });
});
