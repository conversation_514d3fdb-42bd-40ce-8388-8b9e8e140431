// controllers/pressureUlcerController.js
const pressureUlcerService = require("../services/pressureUlcerServices");

async function createPressureUlcer(req, res) {
  try {
    const pressureUlcer = await pressureUlcerService.createPressureUlcer(
      req.body
    );
    res.status(201).json(pressureUlcer);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
}

async function getPressureUlcer(req, res) {
  try {
    const pressureUlcer = await pressureUlcerService.getPressureUlcer();
    res.status(200).json(pressureUlcer);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}

async function getPressureUlcerById(req, res) {
  try {
    const pressureUlcer = await pressureUlcerService.getPressureUlcerById(
      req.params.id
    );
    res.status(200).json(pressureUlcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function updatePressureUlcer(req, res) {
  try {
    const pressureUlcer = await pressureUlcerService.updatePressureUlcer(
      req.params.id,
      req.body
    );
    res.status(200).json(pressureUlcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function deletePressureUlcer(req, res) {
  try {
    await pressureUlcerService.deletePressureUlcer(req.params.id);
    res.status(200).json({ message: "Lesão por pressão excluida com sucesso" });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  createPressureUlcer,
  getPressureUlcer,
  getPressureUlcerById,
  updatePressureUlcer,
  deletePressureUlcer,
};
