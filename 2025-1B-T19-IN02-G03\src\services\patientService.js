// services/patientService.js
const bcrypt = require("bcryptjs");
const patientRepository = require("../repositories/patientRepository");
const patientSchema = require("../models/patientModel");
const Joi = require("joi"); 

function omitPassword(patient) {
  if (!patient) return null;
  if (Array.isArray(patient)) {
    return patient.map(({ password, ...rest }) => rest);
  }
  const { password, ...rest } = patient;
  return rest;
}

async function getPatientByCPF(cpf) {
  return await patientRepository.findByCPF(cpf);
}

async function createPatient(data) {
  const { error, value } = patientSchema.validate(data, { abortEarly: false });
  if (error) {
    const messages = error.details.map((d) => d.message).join("; ");
    throw new Error(`Erro de validação: ${messages}`);
  }

  const existing = await patientRepository.findByCPF(value.cpf);
  if (existing) {
    throw new Error("CPF já cadastrado.");
  }

  const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS, 10) || 10;
  value.password = await bcrypt.hash(value.password, saltRounds);

  const patient = await patientRepository.create(value);
  return omitPassword(patient);
}

async function getAllPatients() {
  const patients = await patientRepository.findAll();
  return omitPassword(patients);
}

async function getPatientById(id) {
  const patient = await patientRepository.findById(id);
  if (!patient) throw new Error("Paciente não encontrado");
  return omitPassword(patient);
}

async function updatePatient(id, data) {
  const existing = await patientRepository.findById(id);
  if (!existing) throw new Error("Paciente não encontrado");

  const updateSchema = patientSchema
    .fork(Object.keys(patientSchema.describe().keys), (field) =>
      field.optional()
    )
    .min(1)
    .messages({
      "object.min": "Informe ao menos um campo para atualizar.",
    });

  const { error, value } = updateSchema.validate(data, {
    abortEarly: false,
    stripUnknown: true,
  });
  if (error) {
    const messages = error.details.map((d) => d.message).join("; ");
    throw new Error(`Erro de validação: ${messages}`);
  }

  if (value.password) {
    const saltRounds = parseInt(process.env.BCRYPT_SALT_ROUNDS, 10) || 10;
    value.password = await bcrypt.hash(value.password, saltRounds);
  }

  if (value.cpf && value.cpf !== existing.cpf) {
    const other = await patientRepository.findByCPF(value.cpf);
    if (other && other.id !== id)
      throw new Error("CPF já cadastrado para outro paciente.");
  }

  const updated = await patientRepository.update(id, value);
  if (!updated) throw new Error("Falha ao atualizar paciente");
  return omitPassword(updated);
}

async function deletePatient(id) {
  const deleted = await patientRepository.remove(id);
  if (!deleted) throw new Error("Paciente não encontrado");
  return { message: "Paciente deletado com sucesso." };
}

module.exports = {
  createPatient,
  getPatientByCPF,
  getAllPatients,
  getPatientById,
  updatePatient,
  deletePatient,
};
