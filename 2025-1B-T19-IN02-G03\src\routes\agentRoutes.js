// routes/agentRoutes.js
const express = require("express");
const router = express.Router();
const agentController = require("../controllers/agentController");
const { isAuthenticated } = require("../middlewares/authMiddleware");

router.get("/loginAgent", agentController.loginAgent);
router.get("/registerAgent", agentController.registerAgent);
router.post("/login", agentController.processLogin);
router.post("/", agentController.createAgent);
router.get("/formPatient", agentController.formPatient);
router.post("/createPatient", isAuthenticated, agentController.createPatient);

router.get("/homeAgent", isAuthenticated, agentController.homeAgent);
router.get("/info/:id", isAuthenticated, agentController.infoPatient);
router.get("/patient/:id", isAuthenticated, agentController.patientDetail);
router.get("/wound/:id", isAuthenticated, agentController.woundDetail);

router.get("/", agentController.getAgents);
router.get("/:id", agentController.getAgentById);
router.put("/:id", agentController.updateAgent);
router.delete("/:id", agentController.deleteAgent);

module.exports = router;
