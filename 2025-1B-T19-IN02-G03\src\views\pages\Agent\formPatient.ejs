<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cadastrar Paciente e Ficha Médica</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .back-btn::before {
            content: "←";
            font-size: 16px;
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C 0%, #5DB85A 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .title-section h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .content {
            padding: 20px;
            background: #f5f5f5;
            min-height: calc(100vh - 140px);
        }

        .form-container {
            max-width: 600px;
            margin: 0 auto;
        }

        .section-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 20px;
        }

        .section-header {
            background: linear-gradient(135deg, #0D5F0C, #3D963C);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .section-body {
            padding: 25px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            display: block;
        }

        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 16px;
            color: #333;
            background-color: #fff;
            transition: border-color 0.3s, box-shadow 0.3s;
        }

        .form-input:focus {
            outline: none;
            border-color: #3D963C;
            box-shadow: 0 0 0 3px rgba(61, 150, 60, 0.1);
        }

        .form-input:hover {
            border-color: #3D963C;
        }

        .btn-container {
            margin-top: 30px;
            text-align: center;
        }

        .btn-primary {
            background-color: #2d7d2d;
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
            min-width: 200px;
        }

        .btn-primary:hover {
            background-color: #246824;
            transform: translateY(-1px);
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .form-row-three {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .checkbox-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .checkbox-item:hover {
            background: #e9ecef;
        }

        .checkbox-item input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #3D963C;
        }

        .checkbox-text {
            font-size: 14px;
            color: #333;
            user-select: none;
        }

        textarea.form-input {
            resize: vertical;
            min-height: 80px;
            font-family: inherit;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        /* Responsividade para Desktop */
        @media (min-width: 768px) {
            .content {
                padding: 30px;
            }
            
            .form-container {
                max-width: 700px;
            }
            
            .section-body {
                padding: 30px;
            }
            
            .form-group {
                margin-bottom: 25px;
            }
            
            .form-label {
                font-size: 13px;
            }
            
            .form-input {
                font-size: 18px;
                padding: 18px;
            }
        }

        @media (min-width: 1024px) {
            .form-container {
                max-width: 800px;
            }
        }

        /* Responsive para Mobile */
        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title-section h2 {
                font-size: 20px;
            }
            
            .form-row,
            .form-row-three {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .section-header {
                padding: 15px 20px;
                font-size: 1.1rem;
            }
            
            .section-body {
                padding: 20px;
            }
            
            .checkbox-group {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
                gap: 8px;
            }
            
            .checkbox-item {
                padding: 8px;
            }
            
            .checkbox-text {
                font-size: 13px;
            }
            
            .btn-primary {
                width: 100%;
                padding: 15px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">VOLTAR</button>
            <h1>Conferidas+</h1>
        </div>

        <div class="title-section">
            <h2>Cadastrar Paciente e Ficha Médica</h2>
        </div>

        <div class="content">
            <!-- Exibir mensagem de erro se existir -->
            <% if (typeof error !== 'undefined' && error) { %>
                <div class="error-message">
                    <%= error %>
                </div>
            <% } %>

            <form action="/agent/createPatient" method="POST" class="form-container">
                <div class="section-card">
                    <div class="section-header">
                        Dados do Paciente
                    </div>
                    <div class="section-body">
                        <div class="form-group">
                            <label class="form-label">Nome</label>
                            <input type="text" name="name" class="form-input" 
                                   value="<%= typeof formData !== 'undefined' ? formData.name || '' : '' %>" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Telefone (somente números)</label>
                                <input type="text" name="phone" class="form-input" placeholder="11987654321" 
                                       value="<%= typeof formData !== 'undefined' ? formData.phone || '' : '' %>" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Idade</label>
                                <input type="number" name="age" class="form-input" 
                                       value="<%= typeof formData !== 'undefined' ? formData.age || '' : '' %>" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">CPF (formato 000.000.000-00 ou apenas números)</label>
                            <input type="text" name="cpf" class="form-input" 
                                   value="<%= typeof formData !== 'undefined' ? formData.cpf || '' : '' %>" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Senha</label>
                                <input type="password" name="password" class="form-input" required>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="section-card">
                    <div class="section-header">
                        Ficha Médica
                    </div>
                    <div class="section-body">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Etiologia da Ferida</label>
                                <select name="wound_etiology" class="form-input" required>
                                    <option value="">Selecione o tipo de ferida</option>
                                    <option value="Lesão por Pressão">Lesão por Pressão</option>
                                    <option value="Úlcera Venosa">Úlcera Venosa</option>
                                    <option value="Pé Diabético">Pé Diabético</option>
                                    <option value="Outras">Outras</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Localização</label>
                                <select name="location" class="form-input" required>
                                    <option value="">Selecione a localização</option>
                                    <option value="Pé">Pé</option>
                                    <option value="Perna">Perna</option>
                                    <option value="Coxa">Coxa</option>
                                    <option value="Torso">Torso</option>
                                    <option value="Braço">Braço</option>
                                    <option value="Mão">Mão</option>
                                    <option value="Outras">Outra</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row-three">
                            <div class="form-group">
                                <label class="form-label">Largura (cm)</label>
                                <input type="number" step="0.01" name="width" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Comprimento (cm)</label>
                                <input type="number" step="0.01" name="length" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Profundidade (cm)</label>
                                <input type="number" step="0.01" name="depth" class="form-input" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Leito da Ferida</label>
                                <select name="wound_bed" class="form-input" required>
                                    <option value="">Selecione o leito da ferida</option>
                                    <option value="Granulação">Granulação</option>
                                    <option value="Fibrina">Fibrina</option>
                                    <option value="Necrose">Necrose</option>
                                    <option value="Misto">Misto</option>
                                    <option value="Epitelização">Epitelização</option>
                                    <option value="Secreção Purulenta">Secreção Purulenta</option>
                                    <option value="Outras">Outra</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Borda da Ferida</label>
                                <select name="wound_edge" class="form-input" required>
                                    <option value="">Selecione o tipo de borda</option>
                                    <option value="Aderida">Aderida</option>
                                    <option value="Descolada">Descolada</option>
                                    <option value="Esbranquiçada">Esbranquiçada</option>
                                    <option value="Hiperemiada">Hiperemiada</option>
                                    <option value="Macerada">Macerada</option>
                                    <option value="Hiperqueratose">Hiperqueratose</option>
                                    <option value="Outras">Outra</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Exsudato</label>
                            <select name="exudate" class="form-input" required>
                                <option value="">Selecione a presença</option>
                                <option value="true">Presente</option>
                                <option value="false">Ausente</option>
                            </select>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Características do Exsudato</label>
                                <select name="exudate_characteristics" class="form-input" required>
                                    <option value="">Selecione as características</option>
                                    <option value="Seroso">Seroso</option>
                                    <option value="Sanguinolento">Sanguinolento</option>
                                    <option value="Serossanguinolento">Serossanguinolento</option>
                                    <option value="Piossanguinolento">Piossanguinolento</option>
                                    <option value="Purulento">Purulento</option>
                                    <option value="Seropurulento">Seropurulento</option>
                                    <option value="Purulento pútrido">Purulento pútrido</option>
                                    <option value="Outras">Outra</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Intensidade do Exsudato</label>
                                <input type="text" name="exudate_intensity" class="form-input" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Odor</label>
                                <select name="odor" class="form-input" required>
                                    <option value="">Selecione a presença</option>

                                    <option value="true">Presente</option>
                                    <option value="false">Ausente</option>

                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Intensidade do Odor</label>
                                <input type="text" name="odor_intensity" class="form-input" required>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Aparência</label>
                                <select name="appearance" class="form-input" required>
                                    <option value="">Selecione a aparência</option>
                                    <option value="Hiperemiada">Hiperemiada</option>
                                    <option value="Desidratada">Desidratada</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Outras">Outra</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Temperatura</label>
                                <select name="temperature" class="form-input" required>
                                    <option value="">Selecione a temperatura</option>
                                    <option value="Normal">Normal</option>
                                    <option value="Fria">Fria</option>
                                    <option value="Quente">Quente</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">Edema</label>
                                <select name="edema" class="form-input" required>
                                    <option value="">Selecione a presença</option>
                                   <option value="true">Presente</option>
                                    <option value="false">Ausente</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Intensidade do Edema</label>
                                <select name="edema_intensity" class="form-input" required>
                                    <option value="">Selecione a intensidade</option>
                                    <option value="Ausente">Ausente</option>
                                    <option value="Leve (+)">Leve (+)</option>
                                    <option value="Moderado (++)">Moderado (++)</option>
                                    <option value="Severo (+++)">Severo (+++)</option>
                                    <option value="Muito Severo (++++)">Muito Severo (++++)</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row-three">
                            <div class="form-group">
                                <label class="form-label">Dor</label>
                                <select name="pain" class="form-input" required>
                                    <option value="">Selecione a presença</option>
                                   <option value="true">Presente</option>
                                    <option value="false">Ausente</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Intensidade da Dor (0-10)</label>
                                <select name="pain_intensity" class="form-input" required>
                                    <option value="">Selecione a intensidade</option>
                                    <option value="0 - Sem dor">0 - Sem dor</option>
                                    <option value="1-3 - Leve">1-3 - Leve</option>
                                    <option value="4-6 - Moderada">4-6 - Moderada</option>
                                    <option value="7-10 - Severa">7-10 - Severa</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Frequência da Dor</label>
                                <select name="pain_frequency" class="form-input" required>
                                    <option value="">Selecione a frequência</option>
                                    <option value="Contínua">Contínua</option>
                                    <option value="Intermitente">Intermitente</option>
                                    <option value="Ausente">Ausente</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Fatores de Risco</label>
                            <div class="checkbox-group">
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Diabetes">
                                    <span class="checkbox-text">Diabetes</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Hipertensão">
                                    <span class="checkbox-text">Hipertensão</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Tabagismo">
                                    <span class="checkbox-text">Tabagismo</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Obesidade">
                                    <span class="checkbox-text">Obesidade</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Imobilidade">
                                    <span class="checkbox-text">Imobilidade</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Desnutrição">
                                    <span class="checkbox-text">Desnutrição</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Incontinência">
                                    <span class="checkbox-text">Incontinência</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Idade avançada">
                                    <span class="checkbox-text">Idade avançada</span>
                                </label>
                                <label class="checkbox-item">
                                    <input type="checkbox" name="risk_factors" value="Outros">
                                    <span class="checkbox-text">Outros</span>
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">Observações adicionais</label>
                            <textarea name="risk_specifications" class="form-input" rows="3" 
                                      placeholder="Descreva detalhes específicos dos fatores de risco selecionados..."><%= typeof formData !== 'undefined' ? formData.risk_specifications || '' : '' %></textarea>
                        </div> 
                    </div>
                </div>

                <div class="btn-container">
                    <button type="submit" class="btn-primary">
                        CADASTRAR PACIENTE
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        function goBack() {
           window.location.href = '/agent/homeAgent';
        }

        document.querySelector('input[name="cpf"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                value = value.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
                e.target.value = value;
            }
        });

        document.querySelector('input[name="phone"]').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            if (value.length <= 11) {
                e.target.value = value;
            }
        });
    </script>
</body>
</html>