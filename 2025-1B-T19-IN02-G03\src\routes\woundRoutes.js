const express = require('express');
const router = express.Router();
const multer = require('multer');
const upload = multer({ dest: 'uploads/' });
const woundController = require('../controllers/woundController');
const mediaController = require('../controllers/mediaController');

router.get('/wound', woundController.wound);
router.post('/', upload.single('foto'), woundController.createWound);



router.get('/', woundController.getWounds);
router.get('/patient/:patientId/wounds', woundController.listWoundsByPatient);
router.get('/patient/wound/:woundId', woundController.showWoundDetails);
router.get('/:id', woundController.getWoundById);
router.put('/:id', woundController.updateWound);
router.delete('/:id', woundController.deleteWound);


module.exports = router;
