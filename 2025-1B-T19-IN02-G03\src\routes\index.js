const express = require("express");
const router = express.Router();

/*router.get('/', (req, res) => {
  res.send('Rota principal funcionando!');
});*/

// Rota para a página inicial
router.get("/", (req, res) => {
  try {
    res.render("pages/Home");
  } catch (err) {
    console.error("Erro ao renderizar página inicial:", err);
    res.status(500).send("Erro ao carregar a página inicial");
  }
});

module.exports = router;
