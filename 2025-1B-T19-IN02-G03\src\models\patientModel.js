// models/patientModel.js
const Joi = require('joi');


const patientSchema = Joi.object({
  id: Joi.number().integer().positive().optional(),
  name: Joi.string().trim().min(3).max(255).required().messages({
    'string.base': 'Nome deve ser texto.',
    'string.empty': 'Nome não pode ser vazio.',
    'string.min': 'Nome deve ter no mínimo {#limit} caracteres.',
    'string.max': 'Nome deve ter no máximo {#limit} caracteres.',
    'any.required': 'Nome é obrigatório.'
  }),
  phone: Joi.string().trim().pattern(/^\d{10,11}$/).required().messages({ // Ex: 11987654321 ou **********
    'string.base': 'Celular deve ser texto.',
    'string.empty': 'Celular não pode ser vazio.',
    'string.pattern.base': 'Celular deve conter apenas números e ter entre 10 e 11 dígitos.',
    'any.required': 'Celular é obrigatório.'
  }),
  age: Joi.number().integer().min(0).max(120).required().messages({
    'number.base': 'Idade deve ser um número.',
    'number.integer': 'Idade deve ser um número inteiro.',
    'number.min': 'Idade não pode ser menor que {#limit}.',
    'number.max': 'Idade não pode ser maior que {#limit}.',
    'any.required': 'Idade é obrigatória.'
  }),
  cpf: Joi.string().trim().pattern(/^\d{3}\.\d{3}\.\d{3}-\d{2}$|^\d{11}$/).required().messages({
    'string.base': 'CPF deve ser texto.',
    'string.empty': 'CPF não pode ser vazio.',
    'string.pattern.base': 'CPF inválido. Use formato "XXX.XXX.XXX-XX" ou apenas 11 dígitos.',
    'any.required': 'CPF é obrigatório.'
  }),
  password: Joi.string().min(6).required().messages({
    'string.base': 'Senha deve ser texto.',
    'string.empty': 'Senha não pode ser vazia.',
    'string.min': 'Senha deve ter no mínimo {#limit} caracteres.',
    'any.required': 'Senha é obrigatória.'
  }),
  agent_id: Joi.number().integer().positive().required().messages({
    'number.base': 'ID do agente deve ser um número.',
    'number.integer': 'ID do agente deve ser um número inteiro.',
    'number.positive': 'ID do agente deve ser um número positivo.',
    'any.required': 'ID do agente é obrigatório.'
  })
});


module.exports = patientSchema;
