// models/medicalRecordModel.js
const Joi = require('joi');

const medicalRecordSchema = Joi.object({
  id: Joi.number().integer().positive().required(),
  wound_etiology: Joi.string().required(),
  location: Joi.string().required(),
  width: Joi.number().positive().required(),
  length: Joi.number().positive().required(),
  depth: Joi.number().positive().required(),
  wound_bed: Joi.string().required(),
  wound_edge: Joi.string().required(),
  exudate: Joi.string().required(),
  exudate_characteristics: Joi.string().required(),
  exudate_intensity: Joi.string().required(),
  odor: Joi.boolean().required(),
  odor_intensity: Joi.string().required(),
  appearance: Joi.string().required(),
  temperature: Joi.string().required(),
  edema: Joi.boolean().required(),
  edema_intensity: Joi.string().required(),
  pain: Joi.boolean().required(),
  pain_intensity: Joi.string().required(),
  pain_frequency: Joi.string().required(),
  risk_factors: Joi.string().required(),
  risk_specifications: Joi.string().required(),
  agent_id: Joi.number().integer().positive().required(),
  patient_id: Joi.number().integer().positive().required()
});

module.exports = medicalRecordSchema;