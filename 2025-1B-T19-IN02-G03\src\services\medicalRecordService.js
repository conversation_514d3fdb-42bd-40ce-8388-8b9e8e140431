const medicalRecordRepository = require("../repositories/medicalRecordRepository");

function validateRequiredFields(medicalRecord) {
  if (!medicalRecord.wound_etiology || !medicalRecord.location) {
    throw new Error("Etiologia da ferida e localização são obrigatórias.");
  }
}

async function createMedicalRecord(data) {
  validateRequiredFields(data);
  return await medicalRecordRepository.create(data);
}

function getMedicalRecords() {
  return medicalRecordRepository.getAll();
}

async function getMedicalRecordById(id) {  
  const medicalRecord = await medicalRecordRepository.findById(id);
  return medicalRecord;
}

async function getMedicalRecordByPatientId(patientId) {
  const medicalRecord = await medicalRecordRepository.findByPatientId(patientId);
  
  if (!medicalRecord) {
    console.log("Service: Nenhum prontuário encontrado para o patientId:", patientId);
    return null;
  }

  return medicalRecord;
}


async function updateMedicalRecord(id, data) {
  validateRequiredFields(data);
  const updated = await medicalRecordRepository.updateMedicalRecord(id, data);
  if (!updated) throw new Error("Prontuário não encontrado");
  return updated;
}

async function deleteMedicalRecord(id) {
  const deleted = await medicalRecordRepository.delete(id);
  if (!deleted) throw new Error("Prontuário não encontrado");
  return deleted;
}

module.exports = {
  createMedicalRecord,
  getMedicalRecords,
  getMedicalRecordById,
  getMedicalRecordByPatientId,
  updateMedicalRecord,
  deleteMedicalRecord,
};
