const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/diabeticFootUlcerRepository', () => mockRepository);

const diabeticFootUlcerService = require('../../services/diabeticFootUlcerService');

describe('diabeticFootUlcerService', () => {
  const ulceraValida = {
    local: 'Calcanhar direito',
    profundidade: 'superficial',
    infecção: true,
    tratamento: 'Curativo e antibiótico'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createDiabeticFootUlcer', () => {
    it('deve criar uma úlcera do pé diabético', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await diabeticFootUlcerService.createDiabeticFootUlcer(ulceraValida);

      expect(mockRepository.create).toHaveBeenCalledWith(ulceraValida);
      expect(resultado).toEqual(respostaMock);
    });
  });

  describe('getDiabeticFootUlcers', () => {
    it('deve retornar todas as úlceras do pé diabético', () => {
      diabeticFootUlcerService.getDiabeticFootUlcers();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getDiabeticFootUlcerById', () => {
    it('deve retornar uma úlcera se existir', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.findById.mockResolvedValue(respostaMock);

      const resultado = await diabeticFootUlcerService.getDiabeticFootUlcerById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se a úlcera não for encontrada', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(diabeticFootUlcerService.getDiabeticFootUlcerById(999))
        .rejects.toThrow('Úlcera do pé diabético não encontrada');
    });
  });

  describe('updateDiabeticFootUlcer', () => {
    it('deve atualizar a úlcera se existir', async () => {
      const respostaMock = { id: 1, ...ulceraValida };
      mockRepository.update.mockResolvedValue(respostaMock);

      const resultado = await diabeticFootUlcerService.updateDiabeticFootUlcer(1, ulceraValida);

      expect(mockRepository.update).toHaveBeenCalledWith(1, ulceraValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se a úlcera não for encontrada', async () => {
      mockRepository.update.mockResolvedValue(null);

      await expect(diabeticFootUlcerService.updateDiabeticFootUlcer(999, ulceraValida))
        .rejects.toThrow('Úlcera do pé diabético não encontrada');
    });
  });

  describe('deleteDiabeticFootUlcer', () => {
    it('deve deletar a úlcera se existir', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await diabeticFootUlcerService.deleteDiabeticFootUlcer(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se a úlcera não for encontrada para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(diabeticFootUlcerService.deleteDiabeticFootUlcer(1))
        .rejects.toThrow('Úlcera do pé diabético não encontrada');
    });
  });
});
