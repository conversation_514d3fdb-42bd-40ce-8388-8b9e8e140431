<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conferidas - Atualizar Ferida</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .back-btn::before {
            content: "←";
            font-size: 16px;
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C, #4a7c59);
            padding: 30px 20px;
            text-align: center;
        }

        .title {
            color: white;
            font-size: 32px;
            font-weight: bold;
        }

        .form-container {
            padding: 30px 20px;
            max-width: 600px;
            margin: 0 auto;
        }

        .form-section {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .photo-section {
            text-align: center;
            margin-bottom: 30px;
        }

        .photo-instruction {
            font-size: 16px;
            margin-bottom: 20px;
            color: #333;
        }

        .photo-upload {
            width: 200px;
            height: 150px;
            border: 2px dashed #ccc;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px;
            cursor: pointer;
            transition: border-color 0.3s;
        }

        .photo-upload:hover {
            border-color: #4a7c59;
        }

        .photo-upload input {
            display: none;
        }

        .camera-icon {
            font-size: 48px;
            color: #ccc;
        }

        .question {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }

        .options-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 30px;
        }

        .option {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 10px;
            border-radius: 5px;
            cursor: pointer;
        }

        .option input[type="radio"] {
            width: 20px;
            height: 20px;
        }

        .option label {
            cursor: pointer;
            flex: 1;
        }

        .pain-question {
            margin-bottom: 20px;
        }

        .pain-buttons {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 30px;
        }

        .pain-btn {
            width: 80px;
            height: 80px;
            border: 3px solid #4a7c59;
            background-color: #4a7c59;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s;
        }

        .pain-btn.selected {
            background-color: white;
            color: #4a7c59;
        }

        .pain-btn:hover {
            transform: scale(1.1);
        }

        .pain-scale {
            margin: 20px 0;
        }

        .scale-container {
            position: relative;
            height: 60px;
            background: linear-gradient(to right, #4CAF50, #8BC34A, #CDDC39, #FFEB3B, #FFC107, #FF9800, #FF5722);
            border-radius: 30px;
            margin: 20px 0;
        }

        .scale-faces {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 10px;
            height: 100%;
        }

        .face {
            font-size: 48px;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .face:hover {
            transform: scale(1.2);
        }

        .face.selected {
            transform: scale(1.3);
            filter: drop-shadow(0 0 10px rgba(74, 124, 89, 0.5));
        }

        .scale-labels {
            display: flex;
            justify-content: space-between;
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }

        .submit-btn {
            width: 100%;
            background-color: #0D5F0C;
            color: white;
            border: none;
            padding: 15px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .submit-btn:hover {
            background-color: #0a4f0a;
        }

        .debug-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 12px;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title {
                font-size: 28px;
            }
            
            .form-container {
                padding: 20px 15px;
            }
            
            .form-section {
                padding: 20px;
            }
            
            .options-grid {
                grid-template-columns: 1fr;
            }
            
            .pain-buttons {
                flex-wrap: wrap;
                gap: 10px;
            }
            
            .pain-btn {
                width: 60px;
                height: 60px;
                font-size: 12px;
            }
        }

        @media (max-width: 480px) {
            .title {
                font-size: 24px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="history.back()"> VOLTAR </button>
            <h1>Conferidas+</h1>
        </div>

        <div class="title-section">
            <h1 class="title">Nos atualize!</h1>
        </div>

        <div class="form-container">
            

            <form action="/wound/" method="POST" enctype="multipart/form-data">
                <div class="form-section">
                    <div class="photo-section">
                        <p class="photo-instruction">Clique no quadrado abaixo para tirar uma foto da sua ferida:</p>
                        <p class="photo-instruction">Obs: coloque a régua ao lado da ferida</p>
                        <label for="foto-ferida" class="photo-upload" id="photo-label">
                            <input type="file" id="foto-ferida" name="foto" accept="image/*" capture="environment" required>
                            <div class="camera-icon" id="camera-icon">📷</div>
                            <img id="preview-img" style="display: none; max-width: 100%; max-height: 100%; object-fit: cover; border-radius: 10px;" />
                        </label>
                    </div>

                    <div class="question">1. Onde está a ferida?</div>
                    <div class="options-grid">
                        <div class="option">
                            <input type="radio" id="pe-direito" name="localizacao" value="pe-direito" required>
                            <label for="pe-direito">Pé direito</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="braco-esquerdo" name="localizacao" value="braco-esquerdo">
                            <label for="braco-esquerdo">Braço esquerdo</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="pe-esquerdo" name="localizacao" value="pe-esquerdo">
                            <label for="pe-esquerdo">Pé esquerdo</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="perna-direita" name="localizacao" value="perna-direita">
                            <label for="perna-direita">Perna direita</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="braco-direito" name="localizacao" value="braco-direito">
                            <label for="braco-direito">Braço direito</label>
                        </div>
                        <div class="option">
                            <input type="radio" id="perna-esquerda" name="localizacao" value="perna-esquerda">
                            <label for="perna-esquerda">Perna esquerda</label>
                        </div>
                    </div>

                    <div class="pain-question">
                        <div class="question">2. Qual seu nível de dor?</div>
                        <div class="scale-faces">
                            <label class="face">
                                <input type="radio" name="pain_level" value="1" required style="display: none;"> 😊
                            </label>
                            <label class="face">
                                <input type="radio" name="pain_level" value="2" style="display: none;"> 🙂
                            </label>
                            <label class="face">
                                <input type="radio" name="pain_level" value="3" style="display: none;"> 😐
                            </label>
                            <label class="face">
                                <input type="radio" name="pain_level" value="4" style="display: none;"> 😕
                            </label>
                            <label class="face">
                                <input type="radio" name="pain_level" value="5" style="display: none;"> 😰
                            </label>
                            <label class="face">
                                <input type="radio" name="pain_level" value="6" style="display: none;"> 😭
                            </label>
                        </div>

                        <div class="scale-labels">
                            <span>Sem dor</span>
                            <span>Dor leve</span>
                            <span>Dor moderada</span>
                            <span>Dor intensa</span>
                            <span>Dor muito intensa</span>
                            <span>Pior dor possível</span>
                        </div>

                        <div class="question">3. Está coçando?</div>
                        <div class="pain-buttons" data-group="coceira">
                            <button type="button" class="pain-btn" data-value="sim" data-group="coceira">👍<br>Sim</button>
                            <button type="button" class="pain-btn" data-value="nao" data-group="coceira">👎<br>Não</button>
                        </div>
                        <input type="hidden" id="coceira" name="itch" value="">
                    </div>

                    <div class="question">4. Descreva a ferida:</div>
                    <div style="margin-bottom: 30px;">
                        <textarea 
                            name="description" 
                            placeholder="Ex: Ferida aberta com bordas avermelhadas e secreção amarelada"
                            rows="4" 
                            style="width: 100%; padding: 10px; font-size: 16px; border-radius: 8px; border: 1px solid #ccc; resize: vertical;"
                            required
                        ></textarea>
                    </div>

                    <input type="hidden" name="date" id="date" value="">
                    
                    <% if (patient && patient.id) { %>
                        <input type="hidden" name="patient_id" value="<%= patient.id %>">
                    <% } else { %>
                        <input type="hidden" name="patient_id" value="">
                    <% } %>
                    
                    <% if (agent && agent.id) { %>
                        <input type="hidden" name="agent_id" value="<%= agent.id %>">
                    <% } else { %>
                        <input type="hidden" name="agent_id" value="">
                    <% } %>

                    <button type="submit" class="submit-btn">Enviar</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateString = now.toISOString().slice(0, 10);
            document.getElementById('date').value = dateString;
        });

        const painButtons = document.querySelectorAll('.pain-btn');
        painButtons.forEach(button => {
            button.addEventListener('click', () => {
                const group = button.dataset.group;
                const value = button.dataset.value;

                painButtons.forEach(btn => {
                    if (btn.dataset.group === group) {
                        btn.classList.remove('selected');
                    }
                });

                button.classList.add('selected');

                if (group === "coceira") {
                    document.getElementById('coceira').value = (value === "sim") ? "true" : "false";
                }   
            });
        });

        // Adicionar funcionalidade para os emojis de dor
        const faceLabels = document.querySelectorAll('.face');
        faceLabels.forEach(label => {
            label.addEventListener('click', () => {
                // Remove seleção de todos os emojis
                faceLabels.forEach(face => face.classList.remove('selected'));
                // Adiciona seleção ao emoji clicado
                label.classList.add('selected');
                // Marca o radio button correspondente
                const radioButton = label.querySelector('input[type="radio"]');
                if (radioButton) {
                    radioButton.checked = true;
                }
            });
        });

        document.getElementById('foto-ferida').addEventListener('change', function(event) {
            const file = event.target.files[0];
            const preview = document.getElementById('preview-img');
            const icon = document.getElementById('camera-icon');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.src = e.target.result;
                    preview.style.display = 'block';
                    icon.style.display = 'none';
                };
                reader.readAsDataURL(file);
            }
        });

        document.querySelector('form').addEventListener('submit', function(e) {
            const patientId = document.querySelector('input[name="patient_id"]').value;
            const localizacao = document.querySelector('input[name="localizacao"]:checked');
            const painLevel = document.querySelector('input[name="pain_level"]:checked');
            const foto = document.getElementById('foto-ferida').files[0];
            const description = document.querySelector('textarea[name="description"]').value.trim();
            const coceira = document.getElementById('coceira').value;

            if (!patientId) {
                alert('Erro: ID do paciente não encontrado. Recarregue a página e tente novamente.');
                e.preventDefault();
                return;
            }

            if (!painLevel) {
                alert('Por favor, selecione o nível de dor.');
                e.preventDefault();
                return;
            }

            if (!foto) {
                alert('Por favor, tire uma foto da ferida.');
                e.preventDefault();
                return;
            }

            if (!description) {
                alert('Por favor, descreva a ferida.');
                e.preventDefault();
                return;
            }

            if (!coceira) {
                alert('Por favor, informe se a ferida está coçando.');
                e.preventDefault();
                return;
            }
        });
    </script>
</body>
</html>