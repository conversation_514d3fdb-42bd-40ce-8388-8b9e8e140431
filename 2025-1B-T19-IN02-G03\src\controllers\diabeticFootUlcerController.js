const diabeticFootUlcerService = require('../services/diabeticFootUlcerService.js');

async function createDiabeticFootUlcer(req, res) {
  try {
    const ulcer = await diabeticFootUlcerService.createDiabeticFootUlcer(req.body);
    res.status(201).json(ulcer);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
}

async function getDiabeticFootUlcers(req, res) {
  try {
    const ulcers = await diabeticFootUlcerService.getDiabeticFootUlcers();
    res.status(200).json(ulcers);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}

async function getDiabeticFootUlcerById(req, res) {
  try {
    const ulcer = await diabeticFootUlcerService.getDiabeticFootUlcerById(req.params.id);
    res.status(200).json(ulcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function updateDiabeticFootUlcer(req, res) {
  try {
    const ulcer = await diabeticFootUlcerService.updateDiabeticFootUlcer(req.params.id, req.body);
    res.status(200).json(ulcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function deleteDiabeticFootUlcer(req, res) {
  try {
    await diabeticFootUlcerService.deleteDiabeticFootUlcer(req.params.id);
    res.status(200).json({ message: 'Úlcera do pé diabético excluída com sucesso' });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  createDiabeticFootUlcer,
  getDiabeticFootUlcers,
  getDiabeticFootUlcerById,
  updateDiabeticFootUlcer,
  deleteDiabeticFootUlcer
};