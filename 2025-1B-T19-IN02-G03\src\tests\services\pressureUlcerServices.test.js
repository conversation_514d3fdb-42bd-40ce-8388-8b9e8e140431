const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  updatePressureUlcer: jest.fn(),
  delete: jest.fn(),
};

// Mock do repositório
jest.mock('../../repositories/pressureUlcerRepository', () => mockRepository);

const pressureUlcerServices = require('../../services/pressureUlcerServices');

describe('pressureUlcerServices', () => {
  const lesaoValida = {
    grade: '2',
    medical_record_id: 5
  };

  const lesaoInvalida = {
    grade: '',
    medical_record_id: null
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createPressureUlcer', () => {
    it('deve criar lesão com dados válidos', async () => {
      const respostaMock = { id: 1, ...lesaoValida };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await pressureUlcerServices.createPressureUlcer(lesaoValida);

      expect(mockRepository.create).toHaveBeenCalledWith(lesaoValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se campos obrigatórios estiverem ausentes', async () => {
      await expect(pressureUlcerServices.createPressureUlcer(lesaoInvalida))
        .rejects.toThrow('Grau e número de prontuário são obrigatórios.');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getPressureUlcer', () => {
    it('deve retornar todas as lesões por pressão', () => {
      pressureUlcerServices.getPressureUlcer();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getPressureUlcerById', () => {
    it('deve retornar lesão por ID se existir', async () => {
      const respostaMock = { id: 1, ...lesaoValida };
      mockRepository.findById.mockResolvedValue(respostaMock);

      const resultado = await pressureUlcerServices.getPressureUlcerById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se lesão não for encontrada', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(pressureUlcerServices.getPressureUlcerById(999))
        .rejects.toThrow('Lesão por pressão não encontrada');
    });
  });

  describe('updatePressureUlcer', () => {
    it('deve atualizar lesão com dados válidos', async () => {
      const respostaMock = { id: 1, ...lesaoValida };
      mockRepository.updatePressureUlcer.mockResolvedValue(respostaMock);

      const resultado = await pressureUlcerServices.updatePressureUlcer(1, lesaoValida);

      expect(mockRepository.updatePressureUlcer).toHaveBeenCalledWith(1, lesaoValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se lesão não for encontrada', async () => {
      mockRepository.updatePressureUlcer.mockResolvedValue(null);

      await expect(pressureUlcerServices.updatePressureUlcer(999, lesaoValida))
        .rejects.toThrow('Lesão não encontrada');
    });

    it('deve lançar erro se os dados forem inválidos', async () => {
      await expect(pressureUlcerServices.updatePressureUlcer(1, lesaoInvalida))
        .rejects.toThrow('Grau e número de prontuário são obrigatórios.');
    });
  });

  describe('deletePressureUlcer', () => {
    it('deve deletar lesão se encontrada', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await pressureUlcerServices.deletePressureUlcer(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se não encontrar lesão para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(pressureUlcerServices.deletePressureUlcer(1))
        .rejects.toThrow('Prontuário não encontrado');
    });
  });
});
