const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  updateWound: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/woundRepository', () => mockRepository);
jest.mock('../../models/woundModel', () => ({
  validate: jest.fn(),
}));

const woundService = require('../../services/woundService');
const woundSchema = require('../../models/woundModel');

describe('woundService', () => {
  const feridaValida = {
    tipo: 'lacerante',
    profundidade: 'superficial',
    localizacao: 'membro inferior',
  };

  const feridaInvalida = {
    tipo: '',
    profundidade: '',
    localizacao: '',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createWound', () => {
    it('deve criar ferida com dados válidos', async () => {
      woundSchema.validate.mockReturnValue({ value: feridaValida, error: undefined });
      const respostaMock = { id: 1, ...feridaValida };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await woundService.createWound(feridaValida);

      expect(woundSchema.validate).toHaveBeenCalledWith(feridaValida);
      expect(mockRepository.create).toHaveBeenCalledWith(feridaValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se os dados forem inválidos', async () => {
      woundSchema.validate.mockReturnValue({
        error: { details: [{ message: '"tipo" é obrigatório' }] },
      });

      await expect(woundService.createWound(feridaInvalida))
        .rejects.toThrow('"tipo" é obrigatório');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getWounds', () => {
    it('deve retornar todas as feridas', () => {
      woundService.getWounds();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getWoundById', () => {
    it('deve retornar ferida por ID se existir', async () => {
      const respostaMock = { id: 1, ...feridaValida };
      mockRepository.findById.mockResolvedValue(respostaMock);

      const resultado = await woundService.getWoundById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se ferida não for encontrada', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(woundService.getWoundById(999))
        .rejects.toThrow('Wound not found');
    });
  });

  describe('updateWound', () => {
    it('deve atualizar ferida com dados válidos', async () => {
      woundSchema.validate.mockReturnValue({ value: feridaValida, error: undefined });
      const respostaMock = { id: 1, ...feridaValida };
      mockRepository.updateWound.mockResolvedValue(respostaMock);

      const resultado = await woundService.updateWound(1, feridaValida);

      expect(woundSchema.validate).toHaveBeenCalledWith(feridaValida);
      expect(mockRepository.updateWound).toHaveBeenCalledWith(1, feridaValida);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se ferida não for encontrada', async () => {
      woundSchema.validate.mockReturnValue({ value: feridaValida, error: undefined });
      mockRepository.updateWound.mockResolvedValue(null);

      await expect(woundService.updateWound(999, feridaValida))
        .rejects.toThrow('Wound not found');
    });

    it('deve lançar erro se dados inválidos forem fornecidos', async () => {
      woundSchema.validate.mockReturnValue({
        error: { details: [{ message: '"localizacao" é obrigatória' }] },
      });

      await expect(woundService.updateWound(1, feridaInvalida))
        .rejects.toThrow('"localizacao" é obrigatória');
    });
  });

  describe('deleteWound', () => {
    it('deve deletar ferida se encontrada', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await woundService.deleteWound(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se ferida não for encontrada para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(woundService.deleteWound(1))
        .rejects.toThrow('Wound not found');
    });
  });
});