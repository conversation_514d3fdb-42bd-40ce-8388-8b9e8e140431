// repositories/agentRepository.js
const pool = require("../config/db");

class AgentRepository {
  async create(agent) {
    const query = `
      INSERT INTO agent (name, crm, email, password)
      VALUES ($1, $2, $3, $4)
      RETURNING *`;

    const values = [agent.name, agent.crm, agent.email, agent.password];

    const result = await pool.query(query, values);
    return result.rows[0];
  }

  async findByEmail(email) {
    const result = await pool.query("SELECT * FROM agent WHERE email = $1", [
      email,
    ]);
    return result.rows[0];
  }

  
  async  findAgentByPatientId(patientId) {
    const query = `
      SELECT a.id, a.name, a.email -- campos que desejar
      FROM agent a
      JOIN patient p ON p.agent_id = a.id
      WHERE p.id = $1
    `;
    const result = await db.query(query, [patientId]);
    return result.rows[0] || null;
  }

  async findByCrm(crm) {
    const result = await pool.query("SELECT * FROM agent WHERE crm = $1", [
      crm,
    ]);
    return result.rows[0];
  }

  async getAll() {
    const result = await pool.query("SELECT * FROM agent");
    return result.rows;
  }

  async findAgentByPatientId(patientId) {
  const query = `
    SELECT a.id, a.name, a.email
    FROM agent a
    JOIN patient p ON p.agent_id = a.id
    WHERE p.id = $1
  `;
  const result = await pool.query(query, [patientId]);  
  return result.rows[0] || null;
}


  async update(id, agent) {
    const query = `
      UPDATE agent SET
        name = $1,
        crm = $2,
        email = $3,
        password = $4
      WHERE id = $5
      RETURNING *`;

    const values = [agent.name, agent.crm, agent.email, agent.password, id];

    const result = await pool.query(query, values);
    return result.rows[0];
  }

  async delete(id) {
    const result = await pool.query(
      "DELETE FROM agent WHERE id = $1 RETURNING *",
      [id]
    );
    return result.rows[0];
  }
}

module.exports = new AgentRepository();
