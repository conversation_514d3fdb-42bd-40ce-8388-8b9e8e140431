const mediaService = require("../../services/mediaService");

const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  updateMedia: jest.fn(),
  delete: jest.fn(),
};

jest.mock("../../repositories/mediaRepository", () => mockRepository);
jest.mock("../../models/mediaModel", () => ({
  validate: jest.fn(),
}));

const mockSchema = require("../../models/mediaModel");

describe("mediaService", () => {
  const midiaValida = {
    type: "imagem",
    path: "https://exemplo.com/imagem.jpg",
    wound_id: 1,
  };

  const midiaInvalida = {
    type: "",
    path: "",
    wound_id: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("createMedia", () => {
    it("deve criar uma mídia válida", async () => {
      mockSchema.validate.mockReturnValue({
        value: midiaValida,
        error: undefined,
      });
      const respostaMock = { id: 1, ...midiaValida };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await mediaService.createMedia(midiaValida);

      expect(mockSchema.validate).toHaveBeenCalledWith(midiaValida);
      expect(mockRepository.create).toHaveBeenCalledWith(midiaValida);
      expect(resultado).toEqual(respostaMock);
    });

    it("deve lançar erro se os dados forem inválidos", async () => {
      mockSchema.validate.mockReturnValue({
        error: { details: [{ message: '"type" é obrigatório' }] },
      });

      await expect(mediaService.createMedia(midiaInvalida)).rejects.toThrow(
        '"type" é obrigatório'
      );
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe("getMedia", () => {
    it("deve retornar todas as mídias", async () => {
      mockRepository.getAll.mockResolvedValue([{ id: 1 }]);
      const resultado = await mediaService.getMedia();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
      expect(resultado).toEqual([{ id: 1 }]);
    });
  });

  describe("getMediaById", () => {
    it("deve retornar mídia se encontrada", async () => {
      const midia = { id: 1, ...midiaValida };
      mockRepository.findById.mockResolvedValue(midia);

      const resultado = await mediaService.getMediaById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(midia);
    });

    it("deve lançar erro se mídia não for encontrada", async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(mediaService.getMediaById(999)).rejects.toThrow(
        "Media not found"
      );
    });
  });

  describe("updateMedia", () => {
    it("deve atualizar a mídia se os dados forem válidos", async () => {
      mockSchema.validate.mockReturnValue({
        value: midiaValida,
        error: undefined,
      });
      const respostaMock = { id: 1, ...midiaValida };
      mockRepository.updateMedia.mockResolvedValue(respostaMock);

      const resultado = await mediaService.updateMedia(1, midiaValida);

      expect(mockSchema.validate).toHaveBeenCalledWith(midiaValida);
      expect(mockRepository.updateMedia).toHaveBeenCalledWith(1, midiaValida);
      expect(resultado).toEqual(respostaMock);
    });

    it("deve lançar erro se a mídia não for encontrada para atualizar", async () => {
      mockSchema.validate.mockReturnValue({
        value: midiaValida,
        error: undefined,
      });
      mockRepository.updateMedia.mockResolvedValue(null);

      await expect(mediaService.updateMedia(99, midiaValida)).rejects.toThrow(
        "Media not found"
      );
    });

    it("deve lançar erro se dados inválidos forem fornecidos", async () => {
      mockSchema.validate.mockReturnValue({
        error: { details: [{ message: '"path" é obrigatória' }] },
      });

      await expect(mediaService.updateMedia(1, midiaInvalida)).rejects.toThrow(
        '"path" é obrigatória'
      );
    });
  });

  describe("deleteMedia", () => {
    it("deve deletar mídia se encontrada", async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await mediaService.deleteMedia(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it("deve lançar erro se a mídia não for encontrada para deletar", async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(mediaService.deleteMedia(1)).rejects.toThrow(
        "Media not found"
      );
    });
  });
});
