<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Peça Ajuda!</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #2d5a2d 0%, #4a7c4a 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .back-button {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px;
            border-radius: 8px;
            transition: background-color 0.2s;
        }

        .back-button:hover {
            background-color: rgba(255,255,255,0.1);
        }

        .status {
            font-size: 14px;
            opacity: 0.9;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 40px 20px;
            text-align: center;
        }

        .title {
            font-size: 28px;
            font-weight: 600;
            color: #2d5a2d;
            margin-bottom: 40px;
            text-shadow: 1px 1px 2px rgba(255,255,255,0.5);
        }

        .instructions {
            background: white;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 50px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
        }

        .instructions p {
            font-size: 16px;
            color: #333;
            margin-bottom: 15px;
            line-height: 1.5;
        }

        .instructions ul {
            list-style: none;
            text-align: left;
            margin: 20px 0;
        }

        .instructions li {
            font-size: 15px;
            color: #555;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .instructions li::before {
            content: "•";
            color: #4a7c4a;
            font-weight: bold;
            position: absolute;
            left: 0;
        }

        .emergency-button {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background: linear-gradient(135deg, #7fb87f 0%, #5fa85f 100%);
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(127, 184, 127, 0.4);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .emergency-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 35px rgba(127, 184, 127, 0.6);
        }

        .emergency-button:active {
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(127, 184, 127, 0.5);
        }

        .emergency-icon {
            width: 60px;
            height: 60px;
            background: #d32f2f;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
            position: relative;
        }

        .emergency-icon::after {
            content: "!";
            color: white;
            font-size: 32px;
            font-weight: bold;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -45%);
        }

        .pulse-animation {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            border: 3px solid #7fb87f;
            animation: pulse 2s infinite;
            opacity: 0;
        }

        @keyframes pulse {
            0% {
                transform: scale(1);
                opacity: 1;
            }
            100% {
                transform: scale(1.3);
                opacity: 0;
            }
        }

        .footer {
            background: linear-gradient(135deg, #2d5a2d 0%, #4a7c4a 100%);
            height: 8px;
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 30px 15px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .instructions {
                padding: 25px;
            }
            
            .emergency-button {
                width: 100px;
                height: 100px;
            }
            
            .emergency-icon {
                width: 50px;
                height: 50px;
            }
        }
    </style>
</head>
<body>
    <header class="header">
        <button class="back-button" onclick="goBack()">
            ← VOLTAR
        </button>
        <span class="status">Conferidas</span>
    </header>

    <main class="main-content">
        <h1 class="title">Peça Ajuda!</h1>
        
        <div class="instructions">
            <p>Se você:</p>
            <ul>
                <li>Está sentindo muita dor;</li>
                <li>Precisa de um acompanhamento presencial urgente;</li>
            </ul>
            <p>Clique no botão abaixo:</p>
        </div>

        <button class="emergency-button" onclick="requestHelp()">
            <div class="pulse-animation"></div>
            <div class="emergency-icon"></div>
        </button>
    </main>

    <footer class="footer"></footer>

    <script>
        function goBack() {
            const button = document.querySelector('.back-button');
            button.style.transform = 'scale(0.95)';
            
            setTimeout(() => {
                button.style.transform = 'scale(1)';
                window.history.back();

            }, 150);
        }

        function requestHelp() {
            const button = document.querySelector('.emergency-button');
            
        }

        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.emergency-button');
                        
        });
    </script>
</body>
</html>