CREATE TABLE IF NOT EXISTS agent (
  id SERIAL PRIMARY KEY,
  name VA<PERSON>HAR(100) NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  crm VARCHAR(50) NOT NULL,
  email VARCHAR(250) NOT NULL,
  password VARCHAR(200) NOT NULL
);

CREATE TABLE IF NOT EXISTS patient (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  age INT NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  password VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id)
);

CREATE TABLE IF NOT EXISTS wound (
  id SERIAL PRIMARY KEY,
  pain_level VARCHAR(100) NOT NULL,
  description VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  patient_id INT NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS video (
  id SERIAL PRIMARY KEY,
  external_link VARCHAR(255) NOT NULL,
  description VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  patient_id INT NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS medical_record (
  id SERIAL PRIMARY KEY,
  wound_etiology VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  width VARCHAR(50) NOT NULL,
  length VARCHAR(50) NOT NULL,
  depth VARCHAR(50) NOT NULL,
  wound_bed VARCHAR(255) NOT NULL,
  wound_edge VARCHAR(255) NOT NULL,
  exudate VARCHAR(255) NOT NULL,
  exudate_characteristics VARCHAR(255) NOT NULL,
  exudate_intensity VARCHAR(255) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(255),
  appearance VARCHAR(255) NOT NULL,
  temperature VARCHAR(50) NOT NULL,
  edema BOOLEAN NOT NULL,
  edema_intensity VARCHAR(255),
  pain BOOLEAN NOT NULL,
  pain_intensity VARCHAR(255),
  pain_frequency VARCHAR(255) NOT NULL,
  risk_factors VARCHAR(255) NOT NULL,
  risk_specifications VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  patient_id INT UNIQUE NOT NULL
    REFERENCES patient(id)
);

CREATE TABLE IF NOT EXISTS agent_medical_record (
  id SERIAL PRIMARY KEY,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS media (
  id SERIAL PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  path VARCHAR(255) NOT NULL,
  wound_id INT NOT NULL
    REFERENCES wound(id)
);

CREATE TABLE IF NOT EXISTS pressure_ulcer (
  id SERIAL PRIMARY KEY,
  grade VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS venous_ulcer (
  id SERIAL PRIMARY KEY,
  has_exudate BOOLEAN NOT NULL,
  description VARCHAR(255) NOT NULL,
  intensity VARCHAR(50) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(50) NOT NULL,
  skin_appearance VARCHAR(255) NOT NULL,
  skin_temperature VARCHAR(50) NOT NULL,
  wound_edematous BOOLEAN NOT NULL,
  edema_intensity VARCHAR(50) NOT NULL,
  pain BOOLEAN NOT NULL,
  pain_intensity INT NOT NULL,
  pain_frequency VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS diabetic_foot_ulcer (
  id SERIAL PRIMARY KEY,
  care VARCHAR(255) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);

CREATE TABLE IF NOT EXISTS foot_physical_exam (
  id SERIAL PRIMARY KEY,
  patient_id INT NOT NULL,
  exam_date DATE NOT NULL,
  abnormal_toe_or_foot_shape BOOLEAN NOT NULL,
  callus BOOLEAN NOT NULL,
  mycosis BOOLEAN NOT NULL,
  ingrown_nails BOOLEAN NOT NULL,
  straight_nail_cut BOOLEAN NOT NULL,
  curved_nail_cut BOOLEAN NOT NULL,
  poor_foot_hygiene BOOLEAN NOT NULL,
  wears_synthetic_sock_or_none BOOLEAN NOT NULL,
  wears_inappropriate_footwear BOOLEAN NOT NULL,
  muscle_weakness BOOLEAN NOT NULL,
  macerated_or_dry_skin BOOLEAN NOT NULL,
  interdigital_fissure BOOLEAN NOT NULL,
  amputation BOOLEAN NOT NULL,
  hallux_valgus BOOLEAN NOT NULL,
  edema BOOLEAN NOT NULL,
  muscle_weakness_notes VARCHAR(255) NOT NULL,
  amputation_location VARCHAR(255),
  diabetic_foot_ulcer_id INT NOT NULL
    REFERENCES diabetic_foot_ulcer(id)
);


-- Agentes
INSERT INTO agent (name, CRM, email, password) VALUES
  ('Dra. Ana Silva', 'CRM12345', '<EMAIL>', 'senha123'),
  ('Dr. Carlos Pereira', 'CRM67890', '<EMAIL>', 'senha456');

--Pacientes
INSERT INTO patient (name, phone, age, cpf, password, agent_id) VALUES
 ('João Souza', '11999999999', 45, '12345678901', 'senhaJoao', 11),
  ('Maria Oliveira', '11988888888', 52, '10987654321', 'senhaMaria', 12),
  ('Jose anastacio', '11988882222', 52, '10987654443', 'senhaJose', 12),
  ('Janete Facundes', '11988881224', 45, '10987654233', 'senhaJanete', 11);

-- Feridas
INSERT INTO wound (pain_level, description, agent_id, patient_id) VALUES
  ('Moderada', 'Ferida na perna esquerda', 11, 22),
  ('Leve',     'Ferida no braço direito', 12, 23),
   ('Grave',     'Ferida no braço esquerdo', 12, 24);

-- Vídeos
INSERT INTO video (external_link, description, title, patient_id) VALUES
  ('https://youtu.be/video1', 'Video explicativo da ferida 1', 'Ferida Joao', 22),
  ('https://youtu.be/video2', 'Video explicativo da ferida 2', 'Ferida Maria', 23),
  ('https://youtu.be/video3', 'Video explicativo da ferida 3', 'Ferida Jose', 24);

-- Prontuários
INSERT INTO medical_record (
  wound_etiology, location, width, length, depth,
  wound_bed, wound_edge, exudate, exudate_characteristics, exudate_intensity,
  odor, odor_intensity, appearance, temperature, edema,
  edema_intensity, pain, pain_intensity, pain_frequency,
  risk_factors, risk_specifications, agent_id, patient_id
) VALUES
  ('Trauma',     'Perna esquerda', '5cm', '10cm', '2cm',  'Granuloso', 'Irregular',
   'Sim', 'Seroso', 'Moderado', TRUE, 'Forte', 'Úmido', '37C',
   TRUE, 'Moderado', TRUE, 'Moderada', 'Constante',
   'Diabetes', 'Controle ruim de glicemia', 11, 22),

  ('Queimadura', 'Braço direito',  '3cm', '5cm',  '1cm',  'Necrose',   'Regular',
   'Não', 'Ausente','Baixo',   FALSE, NULL,   'Seco','36.5C',
   FALSE, NULL,     TRUE, 'Leve',     'Ocasional',
   'Hipertensão','Uso de medicamentos', 12, 23),

  ('Corte',      'Braço esquerdo', '4   cm', '6cm',  '0.5cm', 'Granuloso', 'Regular',
   'Sim', 'Purulento', 'Alto', TRUE, 'Moderado', 'Úmido', '37.5C',  
    TRUE, 'Leve', TRUE, 'Moderada', 'Constante',
    'Obesidade', 'Sedentarismo', 12, 24);
  

-- Agente↔Prontuário
INSERT INTO agent_medical_record (agent_id, medical_record_id) VALUES
  (11, 3),
  (12, 4),
  (12, 5);

-- Mídias
INSERT INTO media (type, path, wound_id) VALUES
  ('imagem', '/imagens/ferida1.jpg', 3),
  ('imagem', '/imagens/ferida2.jpg', 4),
  ('imagem',  '/imagens/ferida1.jpg', 5);

-- Lesões de pressão
INSERT INTO pressure_ulcer (grade, medical_record_id) VALUES
  ('Grau II', 3);

-- Úlcera venosa
INSERT INTO venous_ulcer (
  has_exudate, description, intensity, odor, odor_intensity,
  skin_appearance, skin_temperature, wound_edematous, edema_intensity,
  pain, pain_intensity, pain_frequency, medical_record_id
) VALUES
  (TRUE,  'Exsudato moderado', 'Moderado', TRUE, 'Forte',
   'Vermelha','37C', TRUE, 'Moderado',
   TRUE, 3,         'Constante', 4),

    (FALSE, 'Sem exsudato visível', 'Leve', FALSE, 'Moderado',
   'Rosada', '36.5C', FALSE, 'Leve',
   TRUE, 2, 'Intermitente', 5),

   (TRUE, 'Exsudato abundante com secreção amarelada', 'Grave', TRUE, 'Muito forte',
   'Escurecida', '38C', TRUE, 'Grave',
   TRUE, 4, 'Contínua',3);

--Úlcera de pé diabético
INSERT INTO diabetic_foot_ulcer (care, medical_record_id) VALUES
  ('Controle rigoroso de glicemia e cuidados com calçados', 5);

-- -- -- Exame físico de pé
INSERT INTO foot_physical_exam (
  patient_id, exam_date, abnormal_toe_or_foot_shape, callus, mycosis,
  ingrown_nails, straight_nail_cut, curved_nail_cut, poor_foot_hygiene,
  wears_synthetic_sock_or_none, wears_inappropriate_footwear, muscle_weakness,
  macerated_or_dry_skin, interdigital_fissure, amputation, hallux_valgus,
  edema, muscle_weakness_notes, amputation_location, diabetic_foot_ulcer_id
) VALUES
  (24, '2025-05-16', TRUE, TRUE,  FALSE, FALSE, TRUE, FALSE, TRUE,
         TRUE, FALSE, TRUE, FALSE, TRUE, FALSE, TRUE, TRUE, 'Observação leve', NULL, 1);