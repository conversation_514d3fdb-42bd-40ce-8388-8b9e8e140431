const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  updateMedicalRecord: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/medicalRecordRepository', () => mockRepository);

const medicalRecordService = require('../../services/medicalRecordService');

describe('medicalRecordService', () => {
  const prontuarioValido = {
    wound_etiology: 'Úlcera venosa',
    location: 'Perna esquerda'
  };

  const prontuarioInvalido = {
    wound_etiology: '',
    location: ''
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createMedicalRecord', () => {
    it('deve criar prontuário com dados válidos', async () => {
      const respostaMock = { id: 1, ...prontuarioValido };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await medicalRecordService.createMedicalRecord(prontuarioValido);

      expect(mockRepository.create).toHaveBeenCalledWith(prontuarioValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se dados obrigatórios estiverem faltando', async () => {
      await expect(medicalRecordService.createMedicalRecord(prontuarioInvalido))
        .rejects.toThrow('Etiologia da ferida e localização são obrigatórias.');
      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getMedicalRecords', () => {
    it('deve retornar todos os prontuários', () => {
      medicalRecordService.getMedicalRecords();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getMedicalRecordById', () => {
    it('deve retornar prontuário se encontrado', async () => {
      const prontuario = { id: 1, ...prontuarioValido };
      mockRepository.findById.mockResolvedValue(prontuario);

      const resultado = await medicalRecordService.getMedicalRecordById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(prontuario);
    });

    it('deve lançar erro se prontuário não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(medicalRecordService.getMedicalRecordById(99))
        .rejects.toThrow('Prontuário não encontrado');
    });
  });

  describe('updateMedicalRecord', () => {
    it('deve atualizar prontuário com dados válidos', async () => {
      const respostaMock = { id: 1, ...prontuarioValido };
      mockRepository.updateMedicalRecord.mockResolvedValue(respostaMock);

      const resultado = await medicalRecordService.updateMedicalRecord(1, prontuarioValido);

      expect(mockRepository.updateMedicalRecord).toHaveBeenCalledWith(1, prontuarioValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se prontuário não for encontrado', async () => {
      mockRepository.updateMedicalRecord.mockResolvedValue(null);

      await expect(medicalRecordService.updateMedicalRecord(99, prontuarioValido))
        .rejects.toThrow('Prontuário não encontrado');
    });

    it('deve lançar erro se dados forem inválidos', async () => {
      await expect(medicalRecordService.updateMedicalRecord(1, prontuarioInvalido))
        .rejects.toThrow('Etiologia da ferida e localização são obrigatórias.');
    });
  });

  describe('deleteMedicalRecord', () => {
    it('deve deletar prontuário se encontrado', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await medicalRecordService.deleteMedicalRecord(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se prontuário não for encontrado para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(medicalRecordService.deleteMedicalRecord(1))
        .rejects.toThrow('Prontuário não encontrado');
    });
  });
});
