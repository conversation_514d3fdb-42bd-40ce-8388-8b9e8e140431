const pool = require("../config/db");

async function create(data) {
  const { description, pain_level, date, location, patient_id, agent_id, itch } = data;
  const result = await pool.query(
    `INSERT INTO wound (description, pain_level, location, date, patient_id, agent_id, itch)
     VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
    [description, pain_level, location, date, patient_id, agent_id, itch]
  );
  return result.rows[0];
}

async function getAll() {
  const result = await pool.query(`SELECT * FROM wound`);
  return result.rows;
}

async function findById(woundId) {
        const result = await pool.query('SELECT * FROM wound WHERE id = $1', [woundId]);
        return result.rows[0];
    }

async function findMediaByWoundId(woundId) {
        const result = await pool.query('SELECT * FROM media WHERE wound_id = $1', [woundId]);
        return result.rows;
    }

async function findAllByPatientId(patientId) {
  const result = await pool.query(
    'SELECT * FROM wound WHERE patient_id = $1',
    [Number(patientId)]
  );
  return result.rows;
}


async function updateWound(id, data) {
  const { description, pain_level, date, itch } = data;
  const result = await pool.query(
    `UPDATE wound SET description = $1, pain_level = $2, date = $3, itch = $4 WHERE id = $5 RETURNING *`,
    [description, pain_level, date, itch, id]
  );
  return result.rows[0];
}

async function deleteWound(id) {
  const result = await pool.query(
    `DELETE FROM wound WHERE id = $1`,
    [id]
  );
  return result.rowCount > 0;
}

module.exports = {
  create,
  getAll,
  findById,
  findMediaByWoundId,
  findAllByPatientId,
  updateWound,
  delete: deleteWound
};