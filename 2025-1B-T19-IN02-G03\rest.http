#Prontuario--------------------------------

### Buscar todos os prontuários
GET http://localhost:3002/medicalRecord

### Buscar prontuário por ID

GET http://localhost:3002/medicalRecord/5
=======
GET http://localhost:3002/medicalRecord/25


### Criar novo prontuário
POST http://localhost:3002/medicalRecord
Content-Type: application/json

{
  "wound_etiology": "Úlcera venosa",
  "location": "Perna direita",
  "width": 5.0,
  "length": 10.0,
  "depth": 2.0,
  "wound_bed": "Granuloso",
  "wound_edge": "Irregular",
  "exudate": "Sim",
  "exudate_characteristics": "Seroso",
  "exudate_intensity": "Moderado",
  "odor": true,
  "odor_intensity": "Forte",
  "appearance": "Úmido",
  "temperature": "37C",
  "edema": true,
  "edema_intensity": "Moderado",
  "pain": true,
  "pain_intensity": "Moderada",
  "pain_frequency": "Constante",
  "risk_factors": "Diabetes",
  "risk_specifications": "Controle ruim de glicemia",
  "agent_id": 11,
  "patient_id": 29
}
### Atualizar prontuário

PUT http://localhost:3002/medicalRecord/23
=======
PUT http://localhost:3002/medicalRecord/25

Content-Type: application/json

{
  "wound_etiology": "Úlcera venosa atípica",
  "location": "Perna direita",
  "width": 5.0,
  "length": 10.0,
  "depth": 2.0,
  "wound_bed": "Granuloso",
  "wound_edge": "Irregular",
  "exudate": "Sim",
  "exudate_characteristics": "Seroso",
  "exudate_intensity": "Moderado",
  "odor": true,
  "odor_intensity": "Forte",
  "appearance": "Úmido",
  "temperature": "37C",
  "edema": true,
  "edema_intensity": "Moderado",
  "pain": true,
  "pain_intensity": "Moderada",
  "pain_frequency": "Constante",
  "risk_factors": "Diabetes",
  "risk_specifications": "Controle ruim de glicemia",
  "agent_id": 11,
  "patient_id": 29
}

### Deletar prontuário
DELETE http://localhost:3002/medicalRecord/23
=======
DELETE http://localhost:3002/23



# Agents------------------------------

### Buscar todos os agentes
GET http://localhost:3002/agent

### Buscar agente por ID
GET http://localhost:3002/agent/1

### Criar novo agente
POST http://localhost:3002/agent
Content-Type: application/json

{
  "name": "rafaa",
  "crm": "CRM987654",
  "email": "<EMAIL>",
  "password": "123456"
}

### Atualizar agente
PUT http://localhost:3002/agent/1
Content-Type: application/json

{
  "name": "Dra. Joana Safra Campos",
  "crm": "CRM987654",
  "email": "<EMAIL>",
  "password": "novaSenha789"
}

### Deletar agente
DELETE http://localhost:3002/agent/1


#Pacient------------------------------
### Testar conexão com o banco de dados (rota raiz do servidor)
GET http://localhost:3002/

### Buscar todos os pacientes
GET http://localhost:3002/patient

### Buscar paciente por ID
GET http://localhost:3002/patient/2

### Criar novo paciente
POST http://localhost:3002/patient
Content-Type: application/json

{
  "name": "livia",
  "phone": "11987654322",
  "age": 87,
  "cpf": "145.567.789-00",
  "password": "123456",
  "agent_id": 1
}

### Atualizar paciente
PUT http://localhost:3002/patient/5
Content-Type: application/json

{
  "name": "João Pereira",
  "phone": "21998765432",
  "age": 50,
  "cpf": "987.654.321-00",
  "password": "novaSenhaMuitoSegura",
  "agent_id": 1
}

### Deletar paciente
DELETE http://localhost:4001/patient/1


# Pressure Ulcer ------------------------------
### Buscar todos as lesões por presao
GET http://localhost:3002/pressureUlcer/

### Buscar lesão por ID
GET http://localhost:3002/pressureUlcer/5

### Criar nova lesão por pressão
POST http://localhost:3002/pressureUlcer
Content-Type: application/json

{
  "grade": "2° grau",
  "medical_record_id": 3

}



### Atualizar  lesão por pressão

PUT http://localhost:3002/pressureUlcer/5
Content-Type: application/json

{
  "grade": "3° grau",
  "medical_record_id": 3
}
### Deletar  lesão por pressão
DELETE http://localhost:3002/pressureUlcer/5


# Wound ------------------------------
### Buscar todos as feridas
GET http://localhost:3002/wound

### Buscar ferida por ID
GET http://localhost:3002/wound/5

### Criar nova ferida
POST http://localhost:3002/wound
Content-Type: application/json

{
  "pain_level": 7,
  "description": "xxxxxxxxxxx",  
  "date": "2023-10-01",
  "itch": true,
  "patient_id": 5,
  "agent_id": 1
}


### Atualizar ferida
PUT http://localhost:3002/wound/21
Content-Type: application/json

{
  "pain_level": 5,
  "description": "Cleaned and sutured laceration",
  "agent_id": 11,
  "patient_id": 29

}

### Deletar ferida 
DELETE http://localhost:3002/wound/21