# ========================================
# DOCUMENTAÇÃO DA API - SISTEMA DE FERIDAS
# ========================================

# PRONTUÁRIO MÉDICO (Medical Record)
# Endpoint base: /medicalRecord
# Descrição: Gerencia prontuários médicos dos pacientes

### Buscar todos os prontuários
# Retorna: Array de objetos com todos os prontuários
# Status: 200 (sucesso) | 500 (erro interno)
GET http://localhost:3002/medicalRecord

### Buscar prontuário por ID
# Parâmetro: ID do prontuário (número)
# Retorna: Objeto com dados do prontuário específico
# Status: 200 (encontrado) | 404 (não encontrado) | 500 (erro interno)
GET http://localhost:3002/medicalRecord/1

### Criar novo prontuário
# Body: JSON com dados obrigatórios do prontuário
# Retorna: Objeto do prontuário criado com ID gerado
# Status: 201 (criado) | 400 (dados inválidos) | 500 (erro interno)
POST http://localhost:3002/medicalRecord
Content-Type: application/json

{
  "wound_etiology": "Úlcera venosa",
  "location": "Perna direita",
  "width": "5.0",
  "length": "10.0",
  "depth": "2.0",
  "wound_bed": "Granuloso",
  "wound_edge": "Irregular",
  "exudate": "Sim",
  "exudate_characteristics": "Seroso",
  "exudate_intensity": "Moderado",
  "odor": true,
  "odor_intensity": "Forte",
  "appearance": "Úmido",
  "temperature": "37C",
  "edema": true,
  "edema_intensity": "Moderado",
  "pain": true,
  "pain_intensity": "Moderada",
  "pain_frequency": "Constante",
  "risk_factors": "Diabetes",
  "risk_specifications": "Controle ruim de glicemia",
  "agent_id": 1,
  "patient_id": 1
}

### Atualizar prontuário
# Parâmetro: ID do prontuário a ser atualizado
# Body: JSON com dados a serem atualizados
# Retorna: Objeto do prontuário atualizado
# Status: 200 (atualizado) | 404 (não encontrado) | 400 (dados inválidos)
PUT http://localhost:3002/medicalRecord/1
Content-Type: application/json

{
  "wound_etiology": "Úlcera venosa atípica",
  "location": "Perna direita",
  "width": "5.5",
  "length": "10.5",
  "depth": "2.5",
  "wound_bed": "Granuloso",
  "wound_edge": "Irregular",
  "exudate": "Sim",
  "exudate_characteristics": "Seroso",
  "exudate_intensity": "Moderado",
  "odor": true,
  "odor_intensity": "Forte",
  "appearance": "Úmido",
  "temperature": "37C",
  "edema": true,
  "edema_intensity": "Moderado",
  "pain": true,
  "pain_intensity": "Moderada",
  "pain_frequency": "Constante",
  "risk_factors": "Diabetes",
  "risk_specifications": "Controle ruim de glicemia",
  "agent_id": 1,
  "patient_id": 1
}

### Deletar prontuário
# Parâmetro: ID do prontuário a ser deletado
# Retorna: Mensagem de confirmação
# Status: 200 (deletado) | 404 (não encontrado) | 500 (erro interno)
DELETE http://localhost:3002/medicalRecord/1



# ========================================
# AGENTES DE SAÚDE (Agents)
# Endpoint base: /agent
# Descrição: Gerencia profissionais de saúde do sistema

### Buscar todos os agentes
# Retorna: Array de objetos com todos os agentes cadastrados
# Status: 200 (sucesso) | 500 (erro interno)
GET http://localhost:3002/agent

### Buscar agente por ID
# Parâmetro: ID do agente (número)
# Retorna: Objeto com dados do agente específico
# Status: 200 (encontrado) | 404 (não encontrado) | 500 (erro interno)
GET http://localhost:3002/agent/1

### Criar novo agente
# Body: JSON com dados obrigatórios do agente
# Retorna: Objeto do agente criado com ID gerado
# Status: 201 (criado) | 400 (dados inválidos) | 500 (erro interno)
# Campos obrigatórios: name, crm, email, password
POST http://localhost:3002/agent
Content-Type: application/json

{
  "name": "Dr. Rafael Silva",
  "crm": "CRM123456",
  "email": "<EMAIL>",
  "password": "senhaSegura123"
}

### Atualizar agente
# Parâmetro: ID do agente a ser atualizado
# Body: JSON com dados a serem atualizados
# Retorna: Objeto do agente atualizado
# Status: 200 (atualizado) | 404 (não encontrado) | 400 (dados inválidos)
PUT http://localhost:3002/agent/1
Content-Type: application/json

{
  "name": "Dra. Joana Safra Campos",
  "crm": "CRM987654",
  "email": "<EMAIL>",
  "password": "novaSenhaSegura789"
}

### Deletar agente
# Parâmetro: ID do agente a ser deletado
# Retorna: Mensagem de confirmação
# Status: 200 (deletado) | 404 (não encontrado) | 500 (erro interno)
DELETE http://localhost:3002/agent/1


# ========================================
# PACIENTES (Patients)
# Endpoint base: /patient
# Descrição: Gerencia pacientes do sistema

### Testar conexão com o banco de dados (rota raiz do servidor)
# Retorna: Página inicial do sistema
# Status: 200 (sucesso)
GET http://localhost:3002/

### Buscar todos os pacientes
# Retorna: Array de objetos com todos os pacientes cadastrados
# Status: 200 (sucesso) | 500 (erro interno)
GET http://localhost:3002/patient

### Buscar paciente por ID
# Parâmetro: ID do paciente (número)
# Retorna: Objeto com dados do paciente específico
# Status: 200 (encontrado) | 404 (não encontrado) | 500 (erro interno)
GET http://localhost:3002/patient/1

### Criar novo paciente
# Body: JSON com dados obrigatórios do paciente
# Retorna: Objeto do paciente criado com ID gerado
# Status: 201 (criado) | 400 (dados inválidos) | 500 (erro interno)
# Campos obrigatórios: name, phone, age, cpf, password, agent_id
POST http://localhost:3002/patient
Content-Type: application/json

{
  "name": "Lívia Santos",
  "phone": "11987654322",
  "age": 67,
  "cpf": "145.567.789-00",
  "password": "senhaSegura123",
  "agent_id": 1
}

### Atualizar paciente
# Parâmetro: ID do paciente a ser atualizado
# Body: JSON com dados a serem atualizados
# Retorna: Objeto do paciente atualizado
# Status: 200 (atualizado) | 404 (não encontrado) | 400 (dados inválidos)
PUT http://localhost:3002/patient/1
Content-Type: application/json

{
  "name": "João Pereira Silva",
  "phone": "21998765432",
  "age": 50,
  "cpf": "987.654.321-00",
  "password": "novaSenhaMuitoSegura",
  "agent_id": 1
}

### Deletar paciente
# Parâmetro: ID do paciente a ser deletado
# Retorna: Mensagem de confirmação
# Status: 200 (deletado) | 404 (não encontrado) | 500 (erro interno)
# CORREÇÃO: URL estava incorreta (porta 4001 → 3002)
DELETE http://localhost:3002/patient/1


# ========================================
# ÚLCERAS POR PRESSÃO (Pressure Ulcers)
# Endpoint base: /pressureUlcer
# Descrição: Gerencia lesões por pressão específicas

### Buscar todas as lesões por pressão
# Retorna: Array de objetos com todas as úlceras por pressão
# Status: 200 (sucesso) | 500 (erro interno)
GET http://localhost:3002/pressureUlcer

### Buscar lesão por pressão por ID
# Parâmetro: ID da lesão (número)
# Retorna: Objeto com dados da lesão específica
# Status: 200 (encontrado) | 404 (não encontrado) | 500 (erro interno)
GET http://localhost:3002/pressureUlcer/1

### Criar nova lesão por pressão
# Body: JSON com dados obrigatórios da lesão
# Retorna: Objeto da lesão criada com ID gerado
# Status: 201 (criado) | 400 (dados inválidos) | 500 (erro interno)
# Campos obrigatórios: grade, medical_record_id
POST http://localhost:3002/pressureUlcer
Content-Type: application/json

{
  "grade": "2° grau",
  "medical_record_id": 1
}

### Atualizar lesão por pressão
# Parâmetro: ID da lesão a ser atualizada
# Body: JSON com dados a serem atualizados
# Retorna: Objeto da lesão atualizada
# Status: 200 (atualizado) | 404 (não encontrado) | 400 (dados inválidos)
PUT http://localhost:3002/pressureUlcer/1
Content-Type: application/json

{
  "grade": "3° grau",
  "medical_record_id": 1
}

### Deletar lesão por pressão
# Parâmetro: ID da lesão a ser deletada
# Retorna: Mensagem de confirmação
# Status: 200 (deletado) | 404 (não encontrado) | 500 (erro interno)
DELETE http://localhost:3002/pressureUlcer/1


# ========================================
# FERIDAS (Wounds)
# Endpoint base: /wound
# Descrição: Gerencia feridas dos pacientes

### Buscar todas as feridas
# Retorna: Array de objetos com todas as feridas cadastradas
# Status: 200 (sucesso) | 500 (erro interno)
GET http://localhost:3002/wound

### Buscar ferida por ID
# Parâmetro: ID da ferida (número)
# Retorna: Objeto com dados da ferida específica
# Status: 200 (encontrado) | 404 (não encontrado) | 500 (erro interno)
GET http://localhost:3002/wound/1

### Criar nova ferida
# Body: JSON com dados obrigatórios da ferida
# Retorna: Objeto da ferida criada com ID gerado
# Status: 201 (criado) | 400 (dados inválidos) | 500 (erro interno)
# Campos obrigatórios: pain_level, description, patient_id, agent_id
POST http://localhost:3002/wound
Content-Type: application/json

{
  "pain_level": "7",
  "description": "Ferida cirúrgica pós-operatória",
  "date": "2025-01-15",
  "itch": true,
  "patient_id": 1,
  "agent_id": 1
}

### Atualizar ferida
# Parâmetro: ID da ferida a ser atualizada
# Body: JSON com dados a serem atualizados
# Retorna: Objeto da ferida atualizada
# Status: 200 (atualizado) | 404 (não encontrado) | 400 (dados inválidos)
PUT http://localhost:3002/wound/1
Content-Type: application/json

{
  "pain_level": "5",
  "description": "Ferida limpa e suturada, em processo de cicatrização",
  "agent_id": 1,
  "patient_id": 1
}

### Deletar ferida
# Parâmetro: ID da ferida a ser deletada
# Retorna: Mensagem de confirmação
# Status: 200 (deletado) | 404 (não encontrado) | 500 (erro interno)
DELETE http://localhost:3002/wound/1

# ========================================
# ENDPOINTS ADICIONAIS DISPONÍVEIS
# ========================================

### Buscar feridas por paciente
# Parâmetro: ID do paciente
# Retorna: Array com todas as feridas do paciente específico
GET http://localhost:3002/wound/patient/1/wounds

### Detalhes da ferida para paciente
# Parâmetro: ID da ferida
# Retorna: Página com detalhes da ferida (view)
GET http://localhost:3002/wound/patient/wound/1

# ========================================
# ÚLCERAS VENOSAS (Venous Ulcers)
# Endpoint base: /venous-ulcers
# ========================================

### Buscar todas as úlceras venosas
GET http://localhost:3002/venous-ulcers

### Buscar úlcera venosa por ID
GET http://localhost:3002/venous-ulcers/1

### Criar nova úlcera venosa
POST http://localhost:3002/venous-ulcers
Content-Type: application/json

{
  "has_exudate": true,
  "description": "Úlcera venosa em perna direita",
  "intensity": "Moderada",
  "odor": true,
  "odor_intensity": "Leve",
  "skin_appearance": "Eritematosa",
  "skin_temperature": "Normal",
  "wound_edematous": true,
  "edema_intensity": "Moderado",
  "pain": true,
  "pain_intensity": 6,
  "pain_frequency": "Constante",
  "medical_record_id": 1
}

### Atualizar úlcera venosa
PUT http://localhost:3002/venous-ulcers/1
Content-Type: application/json

{
  "has_exudate": false,
  "description": "Úlcera venosa em melhora",
  "intensity": "Leve",
  "medical_record_id": 1
}

### Deletar úlcera venosa
DELETE http://localhost:3002/venous-ulcers/1

# ========================================
# ÚLCERAS DO PÉ DIABÉTICO (Diabetic Foot Ulcers)
# Endpoint base: /diabetic-foot-ulcers
# ========================================

### Buscar todas as úlceras do pé diabético
GET http://localhost:3002/diabetic-foot-ulcers

### Buscar úlcera do pé diabético por ID
GET http://localhost:3002/diabetic-foot-ulcers/1

### Criar nova úlcera do pé diabético
POST http://localhost:3002/diabetic-foot-ulcers
Content-Type: application/json

{
  "care": "Limpeza diária com soro fisiológico e curativo com hidrogel",
  "medical_record_id": 1
}

### Atualizar úlcera do pé diabético
PUT http://localhost:3002/diabetic-foot-ulcers/1
Content-Type: application/json

{
  "care": "Desbridamento realizado, curativo com alginato",
  "medical_record_id": 1
}

### Deletar úlcera do pé diabético
DELETE http://localhost:3002/diabetic-foot-ulcers/1

# ========================================
# MÍDIA (Media)
# Endpoint base: /media
# Descrição: Gerencia arquivos de mídia das feridas
# ========================================

### Buscar todas as mídias
GET http://localhost:3002/media

### Buscar mídia por ID
GET http://localhost:3002/media/1

### Criar nova mídia
POST http://localhost:3002/media
Content-Type: application/json

{
  "type": "image",
  "path": "/uploads/ferida_001.jpg",
  "wound_id": 1
}

### Atualizar mídia
PUT http://localhost:3002/media/1
Content-Type: application/json

{
  "type": "image",
  "path": "/uploads/ferida_001_atualizada.jpg",
  "wound_id": 1
}

### Deletar mídia
DELETE http://localhost:3002/media/1

# ========================================
# EXAME FÍSICO DO PÉ (Foot Physical Exam)
# Endpoint base: /footPhysicalExam
# ========================================

### Buscar todos os exames físicos do pé
GET http://localhost:3002/footPhysicalExam

### Buscar exame físico do pé por ID
GET http://localhost:3002/footPhysicalExam/1

### Criar novo exame físico do pé
POST http://localhost:3002/footPhysicalExam
Content-Type: application/json

{
  "patient_id": 1,
  "exam_date": "2025-01-15",
  "abnormal_toe_or_foot_shape": false,
  "callus": true,
  "mycosis": false,
  "ingrown_nails": false,
  "straight_nail_cut": true,
  "curved_nail_cut": false,
  "poor_foot_hygiene": false,
  "wears_synthetic_sock_or_none": false,
  "wears_inappropriate_footwear": false,
  "muscle_weakness": false,
  "macerated_or_dry_skin": false,
  "interdigital_fissure": false
}

### Atualizar exame físico do pé
PUT http://localhost:3002/footPhysicalExam/1
Content-Type: application/json

{
  "callus": false,
  "straight_nail_cut": true
}

### Deletar exame físico do pé
DELETE http://localhost:3002/footPhysicalExam/1

# ========================================
# VÍDEOS (Videos)
# Endpoint base: /videos
# ========================================

### Buscar todos os vídeos
GET http://localhost:3002/videos

### Buscar vídeo por ID
GET http://localhost:3002/videos/1

### Criar novo vídeo
POST http://localhost:3002/videos
Content-Type: application/json

{
  "external_link": "https://www.youtube.com/watch?v=exemplo",
  "description": "Vídeo educativo sobre cuidados com feridas",
  "title": "Como cuidar de feridas em casa",
  "patient_id": 1
}

### Atualizar vídeo
PUT http://localhost:3002/videos/1
Content-Type: application/json

{
  "title": "Cuidados avançados com feridas",
  "description": "Vídeo atualizado sobre cuidados com feridas"
}

### Deletar vídeo
DELETE http://localhost:3002/videos/1