// services/agentService.js
const agentRepository = require("../repositories/agentRepository");

function validateRequiredFields(agent) {
  if (!agent.name || !agent.crm || !agent.email || !agent.password) {
     throw new Error(
      "Nome, número de registro, email e senha são obrigatórios."
    );
  }
}

async function authenticateAgent(crm, password) {
  const agent = await agentRepository.findByCrm(crm);
  if (!agent) {
    throw new Error("Número de registro ou senha inválidos");
  }
  if (agent.password !== password) {
    throw new Error("Número de registro ou senha inválidos");
  }
  return agent;
}

async function createAgent(data) {
  validateRequiredFields(data);
  return await agentRepository.create(data);
}

function getAgents() {
  return agentRepository.getAll();
}

async function getAgentById(id) {
  const agent = await agentRepository.findById(id);
  if (!agent) throw new Error("Agente não encontrado");
  return agent;
}

async function getAgentByPatientId(patientId) {
  const agent = await agentRepository.findAgentByPatientId(patientId);
  return agent;
}
async function updateAgent(id, data) {
  validateRequiredFields(data);
  const updated = await agentRepository.update(id, data);
  if (!updated) throw new Error("Agente não encontrado");
  return updated;
}

async function deleteAgent(id) {
  const deleted = await agentRepository.delete(id);
  if (!deleted) throw new Error("Agente não encontrado");
  return deleted;
}

module.exports = {
  createAgent,
  getAgentByPatientId,
  getAgents,
  getAgentById,
  updateAgent,
  deleteAgent,
  authenticateAgent,
};
