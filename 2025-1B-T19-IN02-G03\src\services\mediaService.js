const db = require('../config/db');
const mediaRepository = require('../repositories/mediaRepository.js');
const mediaSchema = require('../models/mediaModel.js');

function validate(data) {
  const { error } = mediaSchema.validate(data);
  if (error) throw new Error(error.details[0].message);
}

async function createMedia(mediaData) {
  try {
    console.log("Criando mídia:", mediaData);
    const query = 'INSERT INTO media ( type, wound_id, path) VALUES ($1, $2, $3) RETURNING *';
    const values = [mediaData.type, mediaData.wound_id, mediaData.path];
    const result = await db.query(query, values);
    console.log("Mídia criada:", result.rows[0]);
    return result.rows[0];
  } catch (error) {
    console.error("Erro ao criar mídia:", error);
    throw error;
  }
}

async function getMediaByWoundId(woundId) {
  try { 
    const query = 'SELECT * FROM media WHERE wound_id = $1';
   
    const result = await db.query(query, [woundId]);
    
  
    return result.rows;
  } catch (error) {
    console.error("Erro na query de mídia:", error.message);
    console.error("Stack do erro:", error.stack);
    throw error;
  }
}

async function getMedia() {
  try {
    return await mediaRepository.getAll();
  } catch (error) {
    console.error("Erro ao buscar todas as mídias:", error);
    throw error;
  }
}

async function getMediaById(id) {
  try {
    const item = await mediaRepository.findById(id);
    if (!item) throw new Error('Media not found');
    return item;
  } catch (error) {
    console.error("Erro ao buscar mídia por ID:", error);
    throw error;
  }
}

async function updateMedia(id, data) {
  try {
    validate(data);
    const updated = await mediaRepository.updateMedia(id, data);
    if (!updated) throw new Error('Media not found');
    return updated;
  } catch (error) {
    console.error("Erro ao atualizar mídia:", error);
    throw error;
  }
}

async function deleteMedia(id) {
  try {
    const deleted = await mediaRepository.delete(id);
    if (!deleted) throw new Error('Media not found');
    return true;
  } catch (error) {
    console.error("Erro ao deletar mídia:", error);
    throw error;
  }
}

module.exports = {
  createMedia,
  getMedia,
  getMediaById,
  getMediaByWoundId,
  updateMedia,
  deleteMedia
};