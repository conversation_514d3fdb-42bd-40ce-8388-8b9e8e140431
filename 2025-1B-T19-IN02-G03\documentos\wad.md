<img src="../assets/WAD/logointeli.png">


# WAD - Web Application Document - Módulo 2 - Inteli

**_Os trechos em itálico servem apenas como guia para o preenchimento da seção. Por esse motivo, não devem fazer parte da documentação final_**

## Nome do Grupo

#### Nomes dos integrantes do grupo
*- <PERSON>*

*- <PERSON>*

*- <PERSON><PERSON><PERSON>*

*- <PERSON><PERSON>*

*- <PERSON><PERSON><PERSON>*

*- <PERSON>*

*- <PERSON><PERSON>*




## Sumário

[1. Introdução](#c1)

[2. Visão Geral da Aplicação Web](#c2)

[3. Projeto Técnico da Aplicação Web](#c3)

[4. Desenvolvimento da Aplicação Web](#c4)

[5. Testes da Aplicação Web](#c5)

[6. Estudo de Mercado e Plano de Marketing](#c6)

[7. Conclusões e trabalhos futuros](#c7)

[8. Referências](c#8)

[Anexos](#c9)

<br>


# <a name="c1"></a>1. <PERSON><PERSON><PERSON><PERSON> (sprints 1 a 5)

Segundo a organização científica Wound Healing Society (WHS), feridas crônicas representam um desafio de saúde pública global, afetando cerca de 1 a 2% da população mundial ao longo da vida. No Brasil, estima-se que mais de 13 milhões de pessoas convivem com esse tipo de condição, frequentemente associada a comorbidades, idade avançada e dificuldades de locomoção.
Essa limitação compromete o acesso regular a serviços de saúde, levando muitos pacientes a buscar atendimento apenas quando o quadro já está agravado. Como consequência, há maior sofrimento, queda na qualidade de vida e aumento expressivo dos custos com tratamentos prolongados. As feridas mais comuns no país são úlcera de pé diabético, úlcera venosa e lesão por pressão — todas exigindo cuidados contínuos e monitoramento eficiente.
Diante desse cenário, será desenvolvido um sistema web que conecta pacientes com feridas às Unidades Básicas de Saúde (UBS), criando um modelo de tele acompanhamento, com os seguintes aspectos:

- Agentes de saúde identificarão pacientes nas comunidades e viabilizarão o primeiro contato com os profissionais da UBS.
- O restante do acompanhamento será feito por meio da plataforma, onde os pacientes responderão questionários e enviarão fotos das feridas.
- Haverá lembretes automatizados para garantir atualizações periódicas, caso não haja envio no prazo estipulado, a UBS será alertada.
- Profissionais de saúde monitorarão as imagens e, se necessário, solicitarão que o paciente vá até a unidade para atendimento.


Além disso, vídeos educativos adaptados à etiologia da ferida estarão disponíveis, promovendo o empoderamento do paciente e maior adesão ao tratamento.

A criação de valor está na ampliação do acesso ao cuidado, otimização do tempo dos profissionais de saúde — que poderão atender mais pacientes —, prevenção de agravamentos clínicos e, sobretudo, melhoria da qualidade de vida. Trata-se de um modelo de cuidado mais humano, personalizado e eficiente, voltado especialmente para populações vulneráveis e com dificuldade de locomoção.


# <a name="c2"></a>2. Visão Geral da Aplicação Web (sprint 1)

## 2.1. Escopo do Projeto (sprints 1 e 4)

### 2.1.1. Modelo de 5 Forças de Porter (sprint 1)

O Hospital das Clínicas da Faculdade de Medicina da USP (HCFMUSP) tem sido reconhecido como uma das principais referências no Brasil no tratamento de feridas crônicas por meio da cirurgia plástica, destacando-se pela abordagem multidisciplinar adotada e pela aplicação de técnicas avançadas. Para que esse cenário seja analisado de forma aprofundada, considera-se essencial a utilização de um modelo estratégico consolidado, como o das 5 Forças de Porter (Porter, 1980).

O modelo das 5 Forças de Porter foi desenvolvido por Michael E. Porter e publicado em seu livro Competitive Strategy (Porter, 1980). De acordo com o autor, afirma-se que a lucratividade de um setor é determinada por cinco forças competitivas:

## Rivalidade entre concorrentes – 


Identificam-se os principais concorrentes dentro de um mesmo setor. Fatores como participação de mercado (market share) e o porte das empresas são considerados determinantes para a intensidade da disputa e o nível de competitividade no setor.

Ameaça de novos entrantes – Avalia-se a facilidade ou dificuldade com que novas empresas podem ingressar no setor. Consideram-se fatores como economias de escala, regulamentações locais, poder aquisitivo e existência de monopólios. Quanto maior a dificuldade de entrada, menor é observada a competitividade no setor.

Poder de barganha dos fornecedores – Examina-se a capacidade dos fornecedores de insumos influenciarem preços e condições de mercado. Em casos em que há pouca diversidade de fornecedores, verificou-se que os mesmos podem impor preços mais elevados ou redirecionar a produção para outras empresas, alterando significativamente a dinâmica do setor.

## Poder de barganha dos clientes – 


Analisa-se a influência exercida pelos consumidores sobre os preços e a qualidade dos produtos. Em contextos nos quais os compradores dispõem de diversas opções, constata-se um maior poder de negociação, com possibilidade de pressão por preços mais baixos ou, em casos extremos, de boicotes.

## Ameaça de produtos substitutos –


 Observa-se a existência de empresas que oferecem soluções alternativas capazes de suprir as mesmas necessidades dos clientes, muitas vezes de forma mais acessível. Como exemplo, pode-se citar os serviços Uber/99 em substituição aos táxis. Quanto maior for a disponibilidade dessas alternativas, mais elevado será o risco de perda de market share e de redução nos preços praticados.


Com base na introdução apresentada, justifica-se a aplicação do modelo das 5 forças de Porter ao contexto do HCFMUSP. Tal aplicação é necessária porque essas forças influenciam diretamente a dinâmica do mercado, permitindo que desafios e fraquezas competitivas sejam identificadas e estratégias sejam desenvolvidas para enfrentá-las. Ao realizar-se a análise, constatou-se que o poder de barganha dos fornecedores representa uma preocupação relevante para o HCFMUSP. Embora esse poder de negociação seja moderado, evidenciou-se uma dependência significativa em relação a insumos importados devido à superioridade de qualidade em comparação às opções locais. Além disso, esses insumos específicos apresentam baixa disponibilidade no mercado, conferindo, assim, maior poder de negociação aos fornecedores envolvidos.

O modelo completo das 5 forças de Porter encontra-se detalhado na imagem a seguir:


<div align="center">
  <sub>5 Forças de Porter</sub><br>
<img src="../assets/WAD/5forcas.png" width="95%" alt="5 Forças de Porter"  ><br>
  <sup>Fonte: do próprio grupo</sup>
</div>


| **Força de Porter**                | **Nível**       | **Análise** |
|-----------------------------------|-----------------|-------------|
| **Poder de Barganha dos Fornecedores** | Moderado        | Há dependência de insumos especializados como coberturas de hidrogel e implantes mamários, fornecidos por grandes multinacionais (3M, Medtronic, Smith & Nephew, etc.), o que eleva o poder de barganha dos fornecedores. No entanto, como o HCFMUSP depende de verbas públicas, muitas vezes opta por insumos mais baratos (gazes, soro), reduzindo o poder desses fornecedores. A oscilação orçamentária torna a barganha moderada. |
| **Poder de Barganha dos Clientes**     | Baixo            | Os pacientes atendidos são, em sua maioria, de baixa renda e dependem do SUS. A fila para atendimento é longa, e a possibilidade de migrar para o setor privado ou outras regiões é baixa, o que reduz drasticamente o poder de negociação dos clientes. |
| **Ameaça de Novos Entrantes**          | Baixo/Moderado   | Barreiras como a alta complexidade dos casos, necessidade de infraestrutura hospitalar avançada e equipe multidisciplinar qualificada dificultam a entrada de novos concorrentes. Contudo, avanços tecnológicos e o aumento da demanda por tratamentos ampliam o atrativo do setor, elevando levemente essa ameaça. |
| **Rivalidade entre Concorrentes**      | Moderada         | Há concorrência com instituições privadas (ex: Hospital Mãe de Deus e Santa Izabel), mas o HCFMUSP se destaca por ser referência pública no SUS. A segmentação entre público e privado, e a escassez de instituições com similar estrutura, reduzem a intensidade da rivalidade. |
| **Ameaça de Produtos Substitutos**     | Moderada         | Existem terapias alternativas (ex: substitutos cutâneos, membranas de celulose), mas elas não substituem completamente a cirurgia plástica reconstrutiva em casos complexos. O acesso limitado a essas alternativas também reduz seu impacto como substituto completo. |

<br>

Com base na análise das 5 Forças de Porter aplicada ao tratamento de feridas crônicas via cirurgia plástica no HCFMUSP, é possível concluir que o setor apresenta um ambiente competitivo relativamente equilibrado, mas com características que favorecem a estabilidade da instituição pública frente ao mercado. O poder de barganha dos fornecedores é moderado, influenciado pela concentração do mercado e pelas restrições orçamentárias do setor público. Já o poder de barganha dos clientes é baixo, dado o alto grau de dependência do SUS por parte dos pacientes. A ameaça de novos entrantes permanece reduzida pela complexidade técnica e pela exigência de infraestrutura especializada, embora tendências tecnológicas e demográficas comecem a tornar o mercado mais atraente. A rivalidade entre concorrentes é moderada, mas amenizada pela segmentação entre os sistemas público e privado. Por fim, a ameaça de substitutos também é moderada, uma vez que alternativas terapêuticas ainda não substituem plenamente a atuação cirúrgica em casos complexos.

Assim, o HCFMUSP se encontra em uma posição relativamente segura e estratégica, com poucas ameaças diretas, mas deve manter atenção a inovações tecnológicas e políticas de financiamento público, que podem alterar o equilíbrio competitivo no médio e longo prazo.


### 2.1.2. Análise SWOT da Instituição Parceira (sprint 1)

A Análise SWOT é uma ferramenta estratégica essencial para avaliar a posição de uma empresa no mercado e orientar suas tomadas de decisão. O nome vem das iniciais em inglês de Strengths (Forças), Weaknesses (Fraquezas), Opportunities (Oportunidades) e Threats (Ameaças), que representam os principais fatores internos e externos que impactam o desempenho organizacional (SWOT analysis. Alchemy, 2025.).
Essa análise é dividida em fatores internos e fatores externos da empresa:<br>
<br>

**_Fatores internos:_**<br>
_Forças:_ Identificam os pontos fortes da empresa em relação à concorrência.<br>
_Fraquezas:_ Representam aspectos negativos que precisam ser trabalhados e corrigidos.<br>

**Fatores externos:**<br>
_Oportunidades:_ Identificam possíveis projetos de inovação para crescer no mercado.<br>
_Ameaças:_ Representam riscos externos que podem impactar negativamente o negócio, no qual em maioria dos casos estão fora do controle da empresa. (Luck, 2010).<br>

Dessa forma, a Análise SWOT se torna uma ferramenta essencial para a tomada de decisões estratégicas, permitindo otimizar recursos e direcionar a instituição analisada rumo ao crescimento de forma lógica, demonstrando a importância da análise a seguir sobre o setor de cirurgias plásticas do HC da FMUSP: 

<div align="center">
  <sub>Análise SWOT</sub><br>
  <img src="../assets/WAD/analiseSwot.png" width="90%" 
  alt="SWOT"><br>
  <sup>Fonte: Grupo 3, 2025 (Autoral)</sup>
</div>



| **Forças (Strengths)** | **Fraquezas (Weaknesses)** |
|------------------------|-----------------------------|
| **Alto Reconhecimento**: FMUSP lidera o Ranking SCImago 2025 na América Latina e é 46ª no mundo. HC-FMUSP é o hospital mais bem equipado do Brasil e América Latina segundo HospiRank. | **Reformas Prolongadas**: Parte das salas cirúrgicas da FMUSP ainda passa por reformas. Equipamentos antigos não suportam tecnologias modernas. |
| **Infraestrutura Qualificada**: Hospital das Clínicas é o maior da América Latina, com cerca de 2.400 leitos. | **Filas**: Lesões complexas podem esperar de 3 a 20 meses por cirurgia, o que agrava casos e prejudica o aprendizado prático dos residentes. |
| **Altos Atendimentos**: Unidade de Queimados realiza em média 530 atendimentos mensais, com técnicas que permitem transplantes urgentes. | **Escassez de Profissionais**: Déficit de vagas em residência e longa formação desestimulam especialização em cirurgia plástica. |
| **Banco de Pele**: Único público em SP com capacidade de estocar, processar e doar pele. Participa de missões humanitárias. | **Dependência de Doações**: Materiais como enxertos e curativos avançados dependem de doações. Ausência gera retorno a materiais básicos. |
| **Telemedicina Compactada**: Programa Incor oferece teleconsultas e acompanhamento remoto. Membro ativo da RUTE, promove troca de conhecimento em tempo real. | |

---

| **Oportunidades (Opportunities)** | **Ameaças (Threats)** |
|----------------------------------|------------------------|
| **Integração com IA**: Possibilidade de triagem automatizada por inteligência artificial para priorizar casos críticos. | **Crescente Demanda em Feridas Crônicas**: Aumento de casos ligados a diabetes pode sobrecarregar leitos e unidades de saúde. |
| **Eventos Pagos**: Realização de eventos como SIMFER-USP 2025 pode gerar renda, promover conhecimento e atrair parcerias. | **Crises Sanitárias**: Pandemias como a COVID-19 impactam o funcionamento das áreas cirúrgicas, com suspensões e superlotações. |
| **Inovação Curricular**: Atualização dos cursos da USP pode incluir mais conteúdo prático e especializado em cirurgia plástica. | **Dependência de Insumos Importados**: Alta nos custos e riscos logísticos por depender de materiais estrangeiros como Pele Alógena e Matriderm. |
| **Ampliação da Telemedicina e Educação Permanente**: Expansão da atuação em redes como a RUTE fortalece a FMUSP na área e amplia o impacto social. | |


Dessa forma, a instituição deve garantir a manutenção de suas forças e aproveitar possíveis oportunidades para continuar com crescimento dentro do mercado. Enquanto mitiga suas ameaças, deve resolver ou diminuir suas fraquezas para garantir sua permanência como um dos principais líderes no setor de educação digital.


### 2.1.3. Solução (sprints 1 a 5)


Problema a ser resolvido:

Pacientes com feridas crônicas enfrentam dificuldade de locomoção e acesso a centros de saúde, causando uma piora de suas condições como dor, queda na qualidade de vida e aumento de custos, o que pode levar ao agravamento das suas condições. Faltam mecanismos eficientes de acompanhamento remoto que integrem os pacientes às UBS de forma contínua e eficaz.


_Dados disponíveis:_
	 Uma ferida crônica se refere àquelas que não conseguiram reparo para produzir a integridade anatômica e funcional em 3 meses[1]. Dentre essas, as úlceras precedem 85% de todas as amputações [2], enquanto a úlcera diabética é o motivo de 70% de todas as amputações de membros inferiores. Da perspectiva financeira, estima-se que os custos decorrentes do tratamento de pacientes com feridas crônicas podem variar entre 1% e 3% das despesas relacionadas à saúde[3].


_Solução proposta:_
	Criar um sistema web com duas interfaces, sendo uma para pacientes e outra para os profissionais de saúde. Ela deve permitir o envio de dados, imagens e vídeos sobre feridas, além de um monitoramento remoto e envio de orientações personalizadas, para promover uma melhor qualidade de vida a pacientes com feridas crônicas.


_Forma de utilização da solução:_
	Agentes de saúde registram os pacientes no sistema em uma plataforma web. Os pacientes enviam informações periódicas por texto, áudio, vídeo ou foto. Dessa forma, os profissionais das UBS monitoram os dados e passam suas devidas orientações. Caso não tenha uma resposta do paciente é gerado alertas para informar a urgência do assunto.


_Benefícios esperados:_
	Estamos esperando uma melhoria no atendimento e acompanhamento pelo SUS, detecção de possíveis feridas, redução de internações, melhores autocuidados do paciente por meio de informações acessíveis e melhor gestão dos recursos de saúde pública.


_Critério de sucesso e como será avaliado:_
	Precisamos que se torne uma rotina do usuário utilizar o aplicativo realizando adequadamente suas tarefas. Para ser avaliado, deveremos realizar testes para garantir uma compreensão intuitiva e verificar se ele engaja o usuário a aprender mais sobre autocuidado.

Ao integrar tecnologia ao cuidado em saúde, a proposta busca transformar o acompanhamento de feridas crônicas em um processo mais ágil, acessível e resolutivo. Acreditamos que a criação de um sistema de monitoramento remoto contribuirá não apenas para a prevenção de agravamentos, mas também para a promoção do autocuidado e uso mais eficiente dos recursos do SUS. O sucesso dessa iniciativa dependerá do engajamento dos usuários e da capacidade da ferramenta em oferecer uma experiência intuitiva, educativa e verdadeiramente transformadora.

### 2.1.4. Value Proposition Canvas (sprint 1): 

O Canvas de Proposta de Valor é uma ferramenta amplamente utilizada no mundo dos negócios devido à sua grande importância. Ele é essencial para compreender e estruturar como um produto ou serviço pode gerar valor para seus clientes. Criado por Alexander Osterwalder, esse modelo faz parte do Business Model Canvas e auxilia as empresas a alinharem suas soluções às reais necessidades do público-alvo. Além disso, essa ferramenta permite mapear tarefas, dores e ganhos dos clientes, ajudando a definir de que forma um produto ou serviço pode minimizar desafios e potencializar benefícios, Pereira (2020).

<div align="center">
  <sub>Value Proposition Canvas</sub><br>
  <img src="../assets/WAD/canvas.png" width="100%" 
  alt="Canvas"><br>
  <sup>Fonte: Grupo 3, 2025 (Autoral)</sup>
</div>
<br><br>

**Cliente**

_Tarefas:_

- Enviar informações sobre sua ferida regularmente → O paciente precisa manter o profissional da saúde atualizado sobre a situação da sua lesão / ferida.

- Aprender sobre a ferida para evitar piora → Só as orientações do profissional não bastam para o sucesso do atendimento, é importante que o paciente consiga adquirir conhecimento suficiente para que, em um momento no qual não há um profissional disponível, ele mesmo saiba como prosseguir.

- Acompanhar sua evolução no tratamento → Para que o processo de atendimento seja eficaz, é importante que toda a evolução do machucado seja registrada, a fim de avaliá-la  a longo prazo e obter uma conclusão mais assertiva.<br><br>


_Dores:_

- Desconfiança em relação à eficácia do tratamento→ Os pacientes não se sentem seguros e confiantes sobre a eficiência do tratamento que os é proposto e, muitas vezes, não seguem corretamente por este motivo. 								

- Falta de conhecimento sobre prevenção e cuidados → As pessoas muitas vezes não têm o conhecimento adequado para saber as causas das feridas, para assim, preveni-las. Ademais, quando já estão com as feridas, não possuem o conhecimento adequado para saber o que fazer para melhorá-las, impedindo a recuperação rápida e adequada.

- Dificuldade de locomoção até uma UBS → As UBS nem sempre são tão próximas das residências, sendo assim, para quem está com feridas, se torna mais complicado ainda, pois possuem dores e dificuldade de locomoção. <br><br>


_Ganhos:_

-Conhecimento sobre como tratar e evitar feridas → Os pacientes têm a oportunidade de aprender a tratar suas feridas, agilizando e auxiliando na recuperação. Além disso, ao saber como evitar feridas, é possível se prevenir e se proteger delas. 

- Acompanhamento profissional de qualidade → É possível ter um acompanhamento detalhado e específico, com uma atenção especial e contínua, fazendo com que o paciente se sinta mais seguro e confie no processo do tratamento.

- Receber instruções personalizadas sem sair de casa → Ao receber as instruções sem sair de casa, o paciente não precisa passar por momentos dolorosos na locomoção, 	possuindo a atenção devida direto do conforto de sua casa, além disso, tem um acompanhamento de seu tratamento e da sua situação com um histórico disponível.<br><br>


**Solução** (Produtos e Serviços do Sistema Web de telemonitoramento de feridas)

_Criador de Ganhos:_

- Acompanhamento com Alertas → Alertas semanais aos pacientes que não atualizarem sua ferida.

-  Evolução Digital → Monitoramento da evolução da ferida do paciente

-  Botão de Emergencia → Um botão one-click pra conectar o paciente ao sistema de emergência.

-  Ultilização de Audio → Audios explicativos para pacientes analfabetos.

_Analgésicos_:

- Acompanhamento remoto: fotos e mensagens pelo sistema →  Um acompanhamento remoto pelo sistema garante que o paciente não precise se locomover até a UBS mais próxima com frequência.


- Checklist interativo com orientações diárias de cuidados→ Orientações diárias promovem o conhecimento sobre a própria saúde ao usuário, fazendo com o que o paciente entenda mais sobre a própria enfermidade.									


- Canal de comunicação direta com o profissional de saúde → Considerando que a desconfiança em relação ao processo é uma dor do cliente, conectá-lo diretamente com um profissional qualificado é a melhor maneira de solucionar esse problema e mostrar ao paciente a veracidade do tratamento.


_Produtos e Serviços:_

- Sistema web com duas interfaces → Uma plataforma conectada, com uma interface para o paciente e outra para o profissional de saúde.

- Armazenamento de dados em nuvem para histórico clínico → Todos os arquivos ficarão salvos na nuvem.

- Sistema web com responsividade → O usuário não necessita de um desktop para utilizar a plataforma, é possível acessá-lo diretamente pelo telefone móvel.

### 2.1.5. Matriz de Riscos do Projeto (sprint 1)

Esta matriz de riscos foi elaborada com o objetivo de identificar, classificar e propor planos de ação para as principais ameaças e oportunidades relacionadas ao desenvolvimento do projeto em andamento. A análise contempla a probabilidade de ocorrência e o impacto potencial de cada item, permitindo uma gestão proativa dos riscos e a maximização dos benefícios identificados.

Estrutura da Matriz

A matriz é composta por dois grandes grupos:

Ameaças: Riscos que podem comprometer o sucesso do projeto.

Oportunidades: Fatores que, se bem aproveitados, podem trazer ganhos significativos.

Cada risco é analisado com base em:

Probabilidade (%): Chance estimada de o risco se concretizar.

Impacto (Baixo / Moderado / Alto / Muito Alto): Consequência esperada caso o risco se realize.

Plano de Ação: Estratégias preventivas ou corretivas adotadas.



<div align="center">
  <sub>Matriz de Riscos</sub><br>
<img src="../assets/WAD/matriz.png.png" width="80%" alt="Matriz de Riscos"  ><br>
  <sup>Fonte: do próprio grupo</sup>
</div>



| Risco                                               | Probabilidade | Impacto    | Justificativa                                                                                                                                                                                                                                        | Plano de Ação                                                                                                                |
|-----------------------------------------------------|---------------|------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|
| Não conclusão do projeto                            | 10%           | Alto       | Compromete a entrega ao parceiro e os objetivos acadêmicos, embora seja pouco provável.                                                                                                                      | Planejar e acompanhar rigorosamente para garantir a conclusão do projeto.                                                   |
| Dificuldade de adoção do produto conforme projetado | 50%           | Moderado   | Resistência a mudanças ou perfis diferentes de usuários podem comprometer a adoção.                                                                                                                           | Testes com usuários, validação constante e adaptação conforme feedback.                                                     |
| Produto final não atende aos objetivos iniciais     | 30%           | Alto       | Mudanças ao longo do desenvolvimento podem desviar do planejamento inicial.                                                                                                                                  | Revisar frequentemente objetivos e cronograma para manter alinhamento.                                                      |
| Entregas quinzenais ineficientes                    | 30%           | Alto       | Entregas com falhas podem causar atrasos e prejudicar a percepção do parceiro.                                                                                                                                | Revisar falhas anteriores e melhorar continuamente as entregas.                                                             |
| Desistência de parceiros                            | 10%           | Muito Alto | Parceiro é peça-chave do projeto, e sua desistência inviabilizaria a entrega como planejada.                                                                                                                  | Comunicação constante e plano alternativo com possíveis novos parceiros.                                                    |
| Falhas no funcionamento do código                   | 50%           | Alto       | Bugs e falhas de lógica comprometem funcionalidades e podem gerar retrabalho.                                                                                                                                | Revisão contínua e correção ágil do código, com ajustes no cronograma.                                                     |
| WAD desatualizado ou desalinhado                    | 50%           | Muito Alto | Desalinhamento entre documentação e implementação pode causar confusões e expectativas erradas.                                                                                                               | Atualizar o WAD regularmente conforme alterações no desenvolvimento.                                                         |
| Incompatibilidade com o TAPI                        | 30%           | Muito Alto | Produto desalinhado com o TAPI compromete a entrega e a aceitação final do parceiro.                                                                                                                         | Revisar o TAPI constantemente para garantir aderência aos requisitos estabelecidos.                                         |

| Oportunidade                                        | Probabilidade | Impacto    | Justificativa                                                                                                                                                                                                 | Plano de Ação                                                                                                                |
|-----------------------------------------------------|---------------|------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------|
| Parceiros comprometidos com o projeto               | 90%           | Muito Alto | Engajamento dos parceiros garante contribuições valiosas e acompanhamento ativo.                                                                                                                           | Manter comunicação frequente e utilizar feedbacks para ajustes e melhorias no projeto.                                     |
| Diversidade de visões na equipe                     | 70%           | Alto       | Diferentes perspectivas estimulam inovação e soluções mais criativas, desde que bem alinhadas.                                                                                                              | Estimular troca de ideias, reuniões de alinhamento e decisões claras sobre direcionamento.                                 |
| Melhoria na eficiência do atendimento               | 70%           | Alto       | Digitalização e prontuário eletrônico agilizam processos, liberando tempo para foco no paciente.                                                                                                            | Treinamentos contínuos, redesenho de fluxos e uso de KPIs para melhoria contínua.                                          |
| Acesso remoto ao histórico do paciente              | 70%           | Alto       | Facilita decisões clínicas fora do hospital, sendo útil para telemedicina e urgências.                                                                                                                      | Garantir segurança, padronização dos dados, capacitação e estabilidade da infraestrutura.                                  |
| Análises preditivas com dados médicos               | 50%           | Alto       | Dados clínicos estruturados permitem prever riscos e apoiar decisões em saúde populacional.                                                                                                                 | Estruturar dados, aplicar BI/ML, validar com projetos-piloto e envolver equipe técnica e clínica.                          |


Conclusão:
A Matriz de Riscos é uma ferramenta essencial para a gestão estratégica do projeto. Ela permite mitigar ameaças de forma preventiva e aproveitar oportunidades com inteligência. A atualização constante da matriz, aliada a uma comunicação efetiva com a equipe e os parceiros, é fundamental para o sucesso do projeto.

## 2.2. Personas (sprint 1)

Protopersonas são representações iniciais e hipotéticas de usuários, criadas com base em suposições da equipe sobre quem são os usuários do produto. Diferente das personas tradicionais, que são fundamentadas em dados reais coletados por meio de pesquisas, as protopersonas servem como um ponto de partida rápido para alinhar o entendimento dentro da equipe sobre os perfis de usuários mais relevantes. Elas ajudam a estruturar discussões e orientar decisões iniciais de design, mesmo antes da realização de estudos aprofundados com usuários reais.

Apesar de serem construídas sem validação empírica, protopersonas ainda seguem o princípio de representar um arquétipo, ou seja, um modelo de usuário típico descrito como se fosse uma pessoa real, com nome, idade, comportamentos, motivações e necessidades. Seu principal valor está em tornar visíveis as suposições da equipe, permitindo que essas ideias possam ser posteriormente validadas com dados concretos.

<div align="center">
  <sub>Persona 1</sub><br>
  <img src="../assets/WAD/Persona1.png" width="60%" 
  alt="Personas"><br>
  <sup>Fonte: Grupo 3, 2025 (Autoral)</sup>
</div>

A segunda protopersona representa uma profissional da área da saúde que atua diretamente no atendimento a pacientes, muitas vezes em campo, enfrentando uma rotina intensa, com responsabilidades variadas e demandas urgentes. Essa representação destaca um perfil de usuário comprometido com o bem-estar da comunidade.

<div align="center">
  <sub>Persona 2</sub><br>
  <img src="../assets/WAD/Persona2.png" width="60%" 
  alt="Personas"><br>
  <sup>Fonte: Grupo 3, 2025 (Autoral)</sup>
</div>

As protopersonas desenvolvidas neste projeto demonstram como é possível antecipar desafios e oportunidades a partir de um olhar mais humano e contextual. Ter personas bem definidas contribui diretamente para decisões de design mais coerentes, funcionais e empáticas.

## 2.3. User Stories (sprints 1 a 5)

Introdução:

Este documento apresenta um conjunto de histórias de usuário elaboradas com base nas necessidades identificadas de diferentes perfis envolvidos no cuidado de pacientes com lesões — incluindo profissionais de saúde, pacientes, cuidadores e administradores da plataforma. As histórias estão estruturadas conforme o modelo ágil de desenvolvimento, contendo critérios de aceite claros e uma análise fundamentada dos princípios INVEST (Independente, Negociável, Valiosa, Estimável, Small, Testável).
O objetivo principal é orientar a construção de funcionalidades priorizadas com foco na usabilidade, segurança da informação, continuidade do cuidado e suporte à tomada de decisão clínica. Cada história reflete um aspecto essencial do fluxo de trabalho e visa garantir que os requisitos do sistema estejam alinhados às necessidades reais dos usuários finais.




Identificação | US1 
--- | ---
Persona 2 | Juliana Silva
User Story | "Como profissional de saúde, quero registrar fotos e informações clínicas da lesão diretamente pelo aplicativo, para que eu possa documentar a evolução visual da lesão ao longo do tratamento."
Critério de aceite 1 | CR1:  O aplicativo permite tirar fotos diretamente ou fazer upload da galeria.
Critério de aceite 2 | CR2: É possível anexar informações clínicas (descrição, data, localização da lesão, observações).
Critério de aceite 3 | CR3: As imagens são organizadas em ordem cronológica.
Critérios INVEST | *Independente, Sim, pois essa funcionalidade não depende de outras para ser desenvolvida; Negociável, Sim, os campos podem ser ajustados conforme necessidade; Valiosa, Sim, contribui diretamente para o acompanhamento clínico; Estimável, Sim, os requisitos são claros e objetivos; Pequena, Sim, pode ser dividida em sub funcionalidades como captura de imagem e preenchimento de informações; Testável, Sim, é possível testar se a imagem foi salva corretamente junto com os dados.*

Identificação | US2
--- | ---
Persona 2 | Juliana Silva
User Story | "Como profissional de saúde, posso classificar a lesão por estágio, profundidade e tipo, para embasar minhas decisões clínicas com base em protocolos estabelecidos."
Critério de aceite 1 | CR1: Sistema apresenta opções padronizadas para classificação (ex: NPUAP, EPUAP). 
Critério de aceite 2 | CR2: É possível selecionar múltiplas características da lesão. 
Critério de aceite 3 | CR3: As classificações são salvas junto com o registro da lesão. 
Critério de aceite 4 | CR4:Alertas são emitidos se houver inconsistência entre classificação e evolução. 
Critérios INVEST | *Independente, pois pode ser desenvolvida sem vínculo direto com outras funcionalidades; Negociável, pois os protocolos usados ou os campos podem ser ajustados; Valiosa, pois padroniza e embasa decisões clínicas; Estimável, pois há clareza sobre o escopo técnico; Pequena, pois pode ser dividida em interface + validações; Testável, pois pode-se verificar se os dados foram salvos corretamente e se os alertas funcionam.*

Identificação | US3
--- | ---
Persona 1 | Lourdes
User Story | "Como paciente, posso visualizar o progresso da minha lesão com fotos e anotações da equipe, para acompanhar minha melhora e ter confiança no tratamento."
Critério de aceite 1 | CR1: O paciente vê uma linha do tempo com imagens e descrições simplificadas. 
Critério de aceite 2 | CR2:  Informações são apresentadas em linguagem acessível. 
Critério de aceite 3 | CR3: É possível visualizar o nome do profissional responsável por cada entrada.
Critério de aceite 4 | CR4: Não há acesso a informações clínicas restritas. 
Critérios INVEST | *Independente, Sim, a visualização não depende de outras funcionalidades específicas; Negociável, Sim, o formato da linha do tempo ou nível de detalhe pode ser ajustado; Valiosa,  Sim, promove engajamento do paciente; Estimável,Sim, pois a interface e os dados são definidos; Pequena, Sim, pode ser separada em visual da linha do tempo e restrição de conteúdo; Testável, Sim, pode-se validar o que o paciente vê e como vê.*

Identificação | US4
--- | ---
Persona 1 | Lourdes
User Story | " Como cuidador, posso registrar alterações na lesão com fotos e anotações simples, para informar a equipe de saúde sobre qualquer mudança."
Critério de aceite 1 | CR1: Interface permite tirar fotos e adicionar comentários.
Critério de aceite 2 | CR2:  O sistema identifica o cuidador que fez o registro.
Critério de aceite 3 | CR3:  Profissionais recebem notificações ao novo registro.
Critério de aceite 4 | CR4:  Há validação de conteúdo antes de ir ao prontuário oficial.
Critérios INVEST | *Independente, Sim, funciona separadamente do profissional de saúde; Negociável, Sim, o fluxo de validação pode ser ajustado; Valiosa, Sim, aumenta a vigilância e segurança do cuidado; Estimável, Sim, envolve captura de mídia e envio com validação; Pequena, Sim, pode ser separada em captura, notificação e validação; Testável, Sim, por meio da criação e recebimento do registro.*

Identificação | US5
--- | ---
Persona 1 | Coordenador Clínico 
User Story | "  Como coordenador clínico, posso visualizar um painel com o status das lesões monitoradas pela equipe, para identificar rapidamente casos críticos e apoiar decisões."
Critério de aceite 1 | CR1: Painel mostra número de lesões por estágio, alertas de piora, e pacientes em risco. 
Critério de aceite 2 | CR2:   É possível filtrar por profissional, unidade ou período. 
Critério de aceite 3 | CR3:  Casos críticos são destacados visualmente.
Critério de aceite 4 | CR4:  O painel é atualizado em tempo real ou com intervalo configurável. 
Critérios INVEST | *Independente,  Sim, funciona mesmo sem acesso a outros módulos; Negociável, Sim, os indicadores e filtros podem ser ajustados; Valiosa, Sim, facilita a gestão clínica de forma proativa; Estimável, Sim, dados e visualizações são definidos; Pequena, Sim, pode ser dividida em visualização + filtros + atualização; Testável, Sim, possível validar dados exibidos e alertas.*

Identificação | US6
--- | ---
Persona 1 | Juliana Silva 
User Story | "  Como profissional de saúde, posso gerar relatórios automáticos com histórico e imagens da lesão, para apresentar evidências em auditorias e facilitar encaminhamentos."
Critério de aceite 1 | CR1: Relatórios incluem fotos, classificações, anotações e intervenções.  
Critério de aceite 2 | CR2: É possível exportar em PDF. 
Critério de aceite 3 | CR3: Pode ser impresso ou enviado por e-mail seguro.
Critério de aceite 4 | CR4: Cabeçalho contém dados do paciente e do profissional responsável.
Critérios INVEST | *Independente, Sim, funciona separadamente da entrada de dados; Negociável, Sim, o formato do relatório pode ser ajustado; Valiosa, Sim, importante para auditorias e encaminhamentos; Estimável, Sim, pois os elementos do relatório são bem definidos; Pequena, Parcial, pode ser quebrada em criação do modelo + exportação + envio; Testável, Sim, pode-se verificar geração e conteúdo do relatório.*

Identificação | US7
--- | ---
Persona 1 | Juliana Silva 
User Story | "Como profissional de saúde, posso configurar lembretes de reavaliação da lesão para cada paciente, para garantir o acompanhamento contínuo e dentro dos prazos clínicos. "
Critério de aceite 1 | CR1:  É possível configurar lembretes com data e hora.  
Critério de aceite 2 | CR2: Sistema envia notificações por push ou e-mail. 
Critério de aceite 3 | CR3:  Lembretes aparecem em uma agenda.
Critério de aceite 4 | CR4: Notificações incluem nome do paciente e lesão.
Critérios INVEST | *Independente, Sim, a configuração de lembretes é autônoma; Negociável, Sim, pode variar o tipo de notificação e prazo; Valiosa, Sim, garante reavaliações no tempo adequado; Estimável, Sim, envolve notificações e calendário simples; Pequena, Parcialmente, pode ser dividida em configuração + envio + exibição; Testável, Sim, é possível simular lembretes e confirmar o envio.*

Identificação | US8
--- | ---
Persona 1 | Lourdes
User Story | " Como paciente, posso receber lembretes personalizados para cuidados diários (ex: troca de curativo), para seguir corretamente o plano e evitar complicações."
Critério de aceite 1 | CR1:  O profissional pode configurar lembretes personalizados.
Critério de aceite 2 | CR2:  Notificações são enviadas via aplicativo e/ou SMS
Critério de aceite 3 | CR3:  O paciente pode confirmar o recebimento e execução.
Critério de aceite 4 | CR4: Histórico dos lembretes é armazenado.
Critérios INVEST | *Independente, Sim, mesmo que a agenda principal não exista; Negociável,  Sim, os canais de notificação ou frequência podem variar; Valiosa, Sim, reforça adesão ao plano de tratamento; Estimável, Sim, com notificações e registro de status; Pequena, Sim, pode ser dividida em configuração, envio e histórico; Testável, Sim, por meio de testes de envio e confirmação.*

Identificação | US9
--- | ---
Persona 1 | Administrador
User Story | " Como administrador da plataforma, posso definir níveis de acesso por tipo de usuário, para proteger dados sensíveis e cumprir normas de privacidade."
Critério de aceite 1 | CR1:  O sistema possui perfis predefinidos (profissional, paciente, cuidador, admin). 
Critério de aceite 2 | CR2: Cada perfil tem permissões específicas configuráveis.
Critério de aceite 3 | CR3: Acesso a dados sensíveis requer autenticação reforçada.
Critério de aceite 4 | CR4: Logs de acesso são mantidos.
Critérios INVEST | *Independente, Sim, pode ser feita isoladamente; Negociável, Sim, os níveis e regras de acesso podem variar; Valiosa, Sim, essencial para segurança e conformidade com LGPD/leis; Estimável, Sim, com escopo claro de perfis e ações; Pequena, Sim, se subdividida em definição de perfis e implementação de permissões; Testável,  Sim, por meio de testes de acesso e visualização de logs.*

Identificação | US10
--- | ---
Persona 1 | Juliana Silva
User Story | " Como profissional de saúde, posso acessar remotamente as informações do paciente, para monitorar o quadro clínico mesmo fora do hospital."
Critério de aceite 1 | CR1:  Sistema permite login seguro via internet. 
Critério de aceite 2 | CR2: Todas as informações clínicas estão acessíveis conforme perfil.
Critério de aceite 3 | CR3: Conexão com criptografia de ponta a ponta.
Critério de aceite 4 | CR4:  Acesso é registrado para auditoria.
Critérios INVEST | *Independente, Sim, pode ser implementado sem outras funcionalidades; Negociável, Sim, pode-se ajustar níveis de acesso ou escopo dos dados; Valiosa, Sim, essencial para a continuidade do cuidado; Estimável, Sim, pois envolve autenticação, visualização e logs; Pequena, Sim, se dividir em segurança, autenticação e exibição; Testável, Sim, testável via simulações de acesso remoto.*

Conclusão:

A documentação das histórias de usuário apresentada neste material serve como base para o desenvolvimento iterativo e incremental de um sistema de monitoramento de lesões. As funcionalidades descritas promovem a integração de registros clínicos visuais, classificação padronizada, comunicação entre atores do cuidado, automação de tarefas administrativas e visibilidade gerencial em tempo real. A aplicação rigorosa dos critérios INVEST garante que cada funcionalidade seja bem definida, negociável, e possível de ser estimada e testada atributos essenciais para um processo de desenvolvimento ágil, eficiente e orientado ao valor entregue. A partir dessas histórias, a equipe de produto pode gerar backlog técnico, validar protótipos com os usuários e priorizar entregas com base em impacto e viabilidade.


# <a name="c3"></a>3. Projeto da Aplicação Web (sprints 1 a 4)

## 3.1. Arquitetura (sprints 3 e 4)

Nossa arquitetura de aplicação web foi construída seguindo o padrão Model-View-Controller (MVC), com o propósito de efetuar uma divisão essencial de responsabilidades entre as camadas, assim facilitando sua manutenção ao longo do projeto. O diagrama exibido  foi separado em três blocos essenciais: Cliente, Servidor e Banco de Dados. Essa separação também permite uma melhor visualização do diagrama, assim como se caracteriza como padrão do MVC.


Já o front-end realiza  e recebe requisições  HTTP com os métodos GET (buscar), POST (criar), PUT (atualizar) e DELETE (apagar) essas requisições chegam diretamente ao controller, que verificam as solicitações e  acionam os services depois repositories até chegaram no models que se conecta com banco de dados. 


| Camada      | Função                                                                                     |
|-------------|--------------------------------------------------------------------------------------------|
| Controller  | Recebe e responde requisições diretamente do cliente.                                      |
| Service     | Responsável pelas regras de negócios.                                                      |
| Repository  | Acessa o banco de dados e realiza buscas específicas (Post/Get), garantindo consultas SQL. |
| Model       | Define o modelo relacional e as tabelas no banco de dados (PostgreSQL).                    |
| View        | Renderiza a página para melhor visualização e interação do cliente.                        |



Tendo em vista essa explicação detalhada das camadas é possível apronfudar sobre o diagrama MVC.


<div align="center">
  <sub>Arquitetura-MVC</sub><br>
  <img src="../assets/WAD/MVC1.png" width="100%" 
  alt="Arquitetura-MVC"><br>
  <sup>Fonte: Grupo 3, 2025 (Autoral)</sup>
</div>

## Views:

## Agent.ejs: 
Cadastro -  Cadastro de Agente
Login - Login de Agente
Visualização - Ver outros agentes


## Patient.ejs
Cadastro - Cadastro de Paciente
Visualização - Visualização dos Pacientes


## Wound.ejs:
Visualização - Ver feridas


## Vídeos.ejs:
Upload - Upload de Vídeos
Lista - Lista de Vídeos


## Medical_Record.ejs:
Visualizar - Visualizar Prontuário
Formulário - Preencher Formulário


## Mídia.ejs:
Visualizar - Ver as fotos enviadas


## Pressure_Ulcer.ejs:
Cadastro - Cadastrar ferida Úlcera Pressão


## Venous_Ulcer.ejs:
Cadastro - Cadastrar ferida Úlcera Venosa


## Diabetic_Foot_Ulcer.ejs:
Cadastro - Cadastrar Úlcera Pé Diabetico



CREATE TABLE IF NOT EXISTS agent (
  id SERIAL PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  crm VARCHAR(50) NOT NULL,
  email VARCHAR(250) NOT NULL,
  password VARCHAR(200) NOT NULL
);



CREATE TABLE IF NOT EXISTS patient (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  phone VARCHAR(20) NOT NULL,
  age INT NOT NULL,
  cpf VARCHAR(20) NOT NULL,
  password VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id)
);



CREATE TABLE IF NOT EXISTS wound (
  id SERIAL PRIMARY KEY,
  pain_level VARCHAR(100) NOT NULL,
  description VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  patient_id INT NOT NULL
    REFERENCES patient(id)
);



CREATE TABLE IF NOT EXISTS video (
  id SERIAL PRIMARY KEY,
  external_link VARCHAR(255) NOT NULL,
  description VARCHAR(255) NOT NULL,
  title VARCHAR(255) NOT NULL,
  patient_id INT NOT NULL
    REFERENCES patient(id)
);



CREATE TABLE IF NOT EXISTS medical_record (
  id SERIAL PRIMARY KEY,
  wound_etiology VARCHAR(255) NOT NULL,
  location VARCHAR(255) NOT NULL,
  width VARCHAR(50) NOT NULL,
  length VARCHAR(50) NOT NULL,
  depth VARCHAR(50) NOT NULL,
  wound_bed VARCHAR(255) NOT NULL,
  wound_edge VARCHAR(255) NOT NULL,
  exudate VARCHAR(255) NOT NULL,
  exudate_characteristics VARCHAR(255) NOT NULL,
  exudate_intensity VARCHAR(255) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(255),
  appearance VARCHAR(255) NOT NULL,
  temperature VARCHAR(50) NOT NULL,
  edema BOOLEAN NOT NULL,
  edema_intensity VARCHAR(255),
  pain BOOLEAN NOT NULL,
  pain_intensity VARCHAR(255),
  pain_frequency VARCHAR(255) NOT NULL,
  risk_factors VARCHAR(255) NOT NULL,
  risk_specifications VARCHAR(255) NOT NULL,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  patient_id INT UNIQUE NOT NULL
    REFERENCES patient(id)
);



CREATE TABLE IF NOT EXISTS agent_medical_record (
  id SERIAL PRIMARY KEY,
  agent_id INT NOT NULL
    REFERENCES agent(id),
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);



CREATE TABLE IF NOT EXISTS media (
  id SERIAL PRIMARY KEY,
  type VARCHAR(50) NOT NULL,
  path VARCHAR(255) NOT NULL,
  wound_id INT NOT NULL
    REFERENCES wound(id)
);



CREATE TABLE IF NOT EXISTS pressure_ulcer (
  id SERIAL PRIMARY KEY,
  grade VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);



CREATE TABLE IF NOT EXISTS venous_ulcer (
  id SERIAL PRIMARY KEY,
  has_exudate BOOLEAN NOT NULL,
  description VARCHAR(255) NOT NULL,
  intensity VARCHAR(50) NOT NULL,
  odor BOOLEAN NOT NULL,
  odor_intensity VARCHAR(50) NOT NULL,
  skin_appearance VARCHAR(255) NOT NULL,
  skin_temperature VARCHAR(50) NOT NULL,
  wound_edematous BOOLEAN NOT NULL,
  edema_intensity VARCHAR(50) NOT NULL,
  pain BOOLEAN NOT NULL,
  pain_intensity INT NOT NULL,
  pain_frequency VARCHAR(50) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);



CREATE TABLE IF NOT EXISTS diabetic_foot_ulcer (
  id SERIAL PRIMARY KEY,
  care VARCHAR(255) NOT NULL,
  medical_record_id INT NOT NULL
    REFERENCES medical_record(id)
);



CREATE TABLE IF NOT EXISTS foot_physical_exam (
  id SERIAL PRIMARY KEY,
  patient_id INT NOT NULL,
  exam_date DATE NOT NULL,
  abnormal_toe_or_foot_shape BOOLEAN NOT NULL,
  callus BOOLEAN NOT NULL,
  mycosis BOOLEAN NOT NULL,
  ingrown_nails BOOLEAN NOT NULL,
  straight_nail_cut BOOLEAN NOT NULL,
  curved_nail_cut BOOLEAN NOT NULL,
  poor_foot_hygiene BOOLEAN NOT NULL,
  wears_synthetic_sock_or_none BOOLEAN NOT NULL,
  wears_inappropriate_footwear BOOLEAN NOT NULL,
  muscle_weakness BOOLEAN NOT NULL,
  macerated_or_dry_skin BOOLEAN NOT NULL,
  interdigital_fissure BOOLEAN NOT NULL,
  amputation BOOLEAN NOT NULL,
  hallux_valgus BOOLEAN NOT NULL,
  edema BOOLEAN NOT NULL,
  muscle_weakness_notes VARCHAR(255) NOT NULL,
  amputation_location VARCHAR(255),
  diabetic_foot_ulcer_id INT NOT NULL
    REFERENCES diabetic_foot_ulcer(id)
);


## 3.2. Wireframes (sprint 2)

Nesta seção, apresentamos os wireframes da plataforma, que representam o esqueleto visual das interfaces tanto para dispositivos móveis quanto para desktop. Wireframes são protótipos de baixa fidelidade que ajudam a planejar a estrutura, a navegação e a disposição dos elementos antes da implementação visual final.

### Objetivo dos Wireframes
- Definir a arquitetura da informação e o fluxo de navegação
- Validar a usabilidade com as personas antes do desenvolvimento
- Servir como referência técnica para designers e desenvolvedores

### Wireframes Desenvolvidos
As telas foram organizadas em duas versões:

#### 1. Versão Mobile
Focada em acessibilidade e experiência em telas menores.  
Prioriza ações essenciais para uso rápido.

![Wireframe mobile](../assets/WAD/wireframe.png)  
*Figura 1: wireframe mobile*

Link de acesso para melhor visualização: [clique aqui](https://miro.com/app/board/uXjVIyzNb8Y=/?track=true&utm_source=notification&utm_medium=email&utm_campaign=approve-request&utm_content=go-to-miro&lid=1v8fyk3ru6qu)


#### 2. Versão Web
Oferece maior espaço para informações detalhadas.  
Inclui ferramentas avançadas para gestão de dados.

![Primeira parte do wireframe web](../assets/WAD/w3.png)  
*Figura 3: Primeira parte do wireframe web*

![Segunda parte do wireframe web](../assets/WAD/w4.png)  
*Figura 4: Segunda parte do wireframe web*

### Próximas Etapas
Quando os wireframes forem validados, nossos próximos passos são:

1. Testes de usabilidade com usuários reais
2. Refinamento do UI Design (cores, tipografia, elementos visuais)
3. Desenvolvimento front-end com base nos protótipos

## 3.3. Guia de estilos (sprint 3)

Antes de avançarmos para a criação do protótipo de alta fidelidade, foi fundamental estabelecer um guia de estilos consistente e alinhado aos objetivos do projeto. Este guia serve como base visual para garantir uma experiência intuitiva, acessível e confiável para os médicos que irão utilizar o sistema para monitorar feridas de pacientes à distância. A seguir, apresentamos as escolhas da paleta de cores e das tipografias, juntamente com suas justificativas, que visam atender às necessidades específicas deste contexto clínico.

<p align="center">
<a><img src="../assets/WAD/guia_de_estilos.png" alt="Guia de estilos"></a>
</p>

[Acesse ao canva aqui](https://www.canva.com/design/DAGo4moEg3w/fhsINAPJ4h5PCgt2uHFRXw/edit)

### 3.3.1 Cores

O **verde** é a cor mais tradicionalmente associada à medicina, simbolizando esperança, equilíbrio e conexão com a natureza. Essa associação reforça valores fundamentais da profissão médica e contribui para transmitir confiança e acolhimento na experiência do usuário.

A escolha da paleta de tons de verde na interface foi feita com o objetivo de transmitir **sensações de saúde, cuidado e confiança**. O verde é uma cor universalmente associada à natureza, cura e bem-estar, o que reforça a temática do site voltado para monitoramento de feridas. Além disso, os diferentes tons de verde ajudam a criar hierarquia visual, facilitando a distinção entre elementos e mantendo a interface visualmente agradável e relaxante para o usuário, que neste caso são os médicos, que precisam de um ambiente calmo e focado para análise clínica.

Essa escolha também reflete a identidade da nossa parceira institucional, a Faculdade de Medicina da USP, que utiliza o verde como cor oficial. Foram usados diversos tons de verde (#61cc33, #00bf24, #56ae20, etc.) para proporcionar contraste e acessibilidade tanto na interface web quanto mobile, melhorando a visualização de textos, botões e ícones.

### 3.3.2 Tipografia

A fonte **Montserrat** foi escolhida para os títulos por sua aparência moderna, limpa e de fácil leitura, transmitindo profissionalismo e seriedade, qualidades essenciais em um ambiente médico. Para os subtítulos e textos, foi adotada a fonte **DM Sans**, altamente legível mesmo em tamanhos menores, o que é fundamental para a leitura rápida e precisa de informações durante o monitoramento remoto de pacientes. A combinação das duas fontes proporciona um contraste visual agradável e contribui para a hierarquia das informações apresentadas.

Durante a pesquisa tipográfica, foram considerados três estilos principais:

- **Sans Serif**: modernas, limpas e associadas à tecnologia e à saúde;
- **Serifadas**: tradicionais, associadas à confiança e autoridade médica;
- **Fontes humanizadas**: que equilibram seriedade com leveza, ideais para uma comunicação voltada à saúde humanizada.

A escolha final priorizou esse terceiro estilo por refletir o tom desejado para o projeto: sério, confiável e acolhedor. As fontes também foram selecionadas por sua boa performance visual em fundos claros e escuros.

### 3.3.3 Iconografia e imagens 

Os ícones foram escolhidos por sua **clareza e representatividade**. Eles são simples, facilmente compreendidos e podem ser adaptados para diferentes fundos (claros ou escuros), alterando suas cores conforme a necessidade de contraste.

- Um destaque especial vai para o _ícone vermelho de alerta_, usado em contextos críticos para chamar atenção imediatamente, como mensagens de erro ou riscos importantes.

Segue abaixo os ícones utilizados no protótipo: 

<p align="center">
<a><img src="../assets/WAD/icones.png" alt="Íncones"></a>
</p>

### Conclusão
Com este guia de estilos, buscamos criar uma identidade visual que não apenas facilite a navegação e a leitura das informações, mas que também transmita segurança, profissionalismo e cuidado, aspectos essenciais para um sistema médico de monitoramento remoto. Essas escolhas visuais são pilares importantes para garantir que os usuários tenham uma experiência eficiente e confiável ao utilizar a plataforma.

## 3.4 Protótipo de alta fidelidade (sprint 3)

Após a definição e validação do wireframe, avançamos para a criação do protótipo de alta fidelidade aperfeiçoando algumas funcionalidades com base nos feedbacks do parceiro para melhor experiência do usuário e priorizando as user stories. Essa etapa foi fundamental para representar com maior precisão o design final da aplicação, incluindo elementos visuais detalhados, tipografia, cores, imagens e interações dinâmicas.

O protótipo de alta fidelidade foi desenvolvido utilizando Figma, permitindo simular a experiência do usuário de forma realista. Essa versão aproximada do produto final foi essencial para testar a usabilidade, o fluxo de navegação e validar o design com stakeholders, antes do início do desenvolvimento.

Segue abaixo o protótipo de alta fidelidade da versão mobile da interface do agente:

<p align="center">
<a><img src="../assets/WAD/prototipo_agente.png" alt="mobile agente"></a>
</p>

Segue abaixo o protótipo de alta fidelidade da versão mobile da interface do paciente:

<p align="center">
<a><img src="../assets/WAD/prototipo_pacientes.png" alt="mobile paciente"></a>
</p>

Segue abaixo o protótipo de alta fidelidade da versão web da interface do agente:
<p align="center">
<a><img src="../assets/WAD/web_agente.png" alt="computador"></a>
</p>

Segue abaixo o protótipo de alta fidelidade da versão web da interface do paciente:
<p align="center">
<a><img src="../assets/WAD/web_paciente.png" alt="computador"></a>
</p>

[Link de acesso aos protótipos aqui](https://www.figma.com/design/OyPc6xFM0ZeYetyFcliKfb/Untitled?node-id=749-2&p=f&t=lzOF2vt53rrshdpi-0)

Desta forma, o protótipo garantiu maior alinhamento entre a equipe de design, desenvolvimento e o cliente, reduzindo retrabalhos e facilitando a comunicação visual do projeto.  A criação do protótipo de alta fidelidade representou um passo decisivo para a concretização do projeto, permitindo transformar conceitos iniciais em uma interface visualmente detalhada e funcional. Essa etapa facilitou a identificação precoce de possíveis melhorias, garantindo uma experiência do usuário mais intuitiva e alinhada aos objetivos do sistema. Com o protótipo validado, a equipe pôde avançar para o desenvolvimento com maior segurança e clareza, aumentando a eficiência do processo e a qualidade do produto final.


## 3.5. Modelagem do banco de dados (sprints 2 e 4)

### 3.5.1. Modelo relacional (sprints 2 e 4)

O modelo de dados relacional é um dos pilares fundamentais da área de banco de dados e representa uma abordagem amplamente utilizada para organizar, armazenar e manipular dados. Baseado em conceitos matemáticos como conjuntos e relações, esse modelo propõe uma estrutura simples e eficiente, onde os dados são representados em tabelas (ou relações) formadas por linhas e colunas.  Nesta seção, será apresentado o modelo relacional desenvolvido para organização dos dados no site Conferidas e suas relações. 

A imagem abaixo apresenta o nosso modelo relacional, que representa a estrutura de relacionamento entre agentes e pacientes no sistema. Nesse modelo, o agente é o responsável por cadastrar novos pacientes, registrar feridas e preencher o prontuário primário, além de preencher os formulários relacionados à ferida específica. Já o paciente, por sua vez, tem acesso a vídeos informativos e pode atualizar os dados referentes à sua ferida.

_Regras de Negócio:_
- Um agente pode cadastrar vários pacientes.
-Cada paciente possui um agente primário responsável pelo seu cadastro, o que estabelece uma chave estrangeira ligando o paciente ao agente.
- Um paciente pode realizar várias atualizações de feridas.
- Um paciente pode acessar diversos vídeos disponíveis no sistema.
- Vários agentes podem preencher diferentes prontuários gerais.
- Cada prontuário geral pode conter três formulários, um para cada ferida registrada.

Modelagem relacional:
<div align="center">
  <sub>Modelo relacional</sub><br>
  <img src="../assets/WAD/modelo-relacional.jpeg" width="80%" 
  alt="Modelo relacional"><br>
  <sup>Fonte: do próprio grupo</sup>
</div>

A partir desse modelo relacional, desenvolvemos um modelo lógico, que serve como uma ponte entre o planejamento conceitual e a implementação técnica. Esse modelo lógico foi essencial para facilitar o desenvolvimento do modelo físico, onde definimos os atributos das entidades, além de estabelecer claramente as chaves primárias e as chaves estrangeiras. Essa etapa garante que o banco de dados seja construído de forma consistente, mantendo a integridade dos dados e refletindo corretamente as regras de negócio da aplicação, como podemos ver no anexo abaixo:

<div align="center">
  <sub>Modelo lógico</sub><br>
  <img src="../assets/WAD/diagrama-logico.png" width="80%" 
  alt="Modelo lógico"><br>
  <sup>Fonte: do próprio grupo</sup>
</div>

[Confira o modelo lógico ](https://dbdiagram.io/d/modelo-681cf48d5b2fc4582fced9ca)

A construção de um modelo de dados relacional e lógico foi essencial para organizarmos de forma clara e eficiente como as informações seriam tratadas dentro do sistema. Esse processo não apenas nos permitiu entender quais dados precisavam ser armazenados, mas também como esses dados se relacionam entre si.
Ao estruturar o fluxo de dados com base nesse modelo, conseguimos mapear toda a lógica da aplicação, garantindo que as informações trafeguem corretamente entre as diferentes partes do sistema. Isso foi especialmente importante para manter a integridade dos dados, evitar redundâncias e aplicar de forma consistente as regras de negócio.


### 3.5.2. Consultas SQL e lógica proposicional (sprint 2)

Na aplicação de monitoramento de feridas, é necessário realizar diversas consultas ao banco de dados para que os profissionais de saúde possam acessar, analisar e acompanhar as informações dos pacientes e suas feridas de forma organizada e segura. Essas consultas utilizam a linguagem SQL (Structured Query Language), que permite buscar, inserir, atualizar ou remover informações armazenadas no sistema.

### CONSULTA 1 – PACIENTE, FERIDA E MÍDIA

Essa consulta identifica todos os pacientes ativos que têm feridas cadastradas e cuja última imagem enviada da ferida foi há mais de 7 dias. Isso permite alertar os profissionais responsáveis sobre pacientes que não estão cumprindo a periodicidade exigida de envio semanal de imagens.


CONSULTA 1 | HANDBOOK E ASSEMBLELINE
--- | ---
**Expressão SQL** | SELECT p.id, p.nome<br>FROM paciente p<br>JOIN ferida ON ferida.id_associatedPaciente = p.id<br>JOIN midia ON midia.id_associatedFerida = ferida.id<br>WHERE p.status = 'ativo'<br>AND midia.tipo = 'imagem'<br>GROUP BY p.id, paciente.nome<br>HAVING MAX(midia.data_envio) < CURRENT_DATE - INTERVAL '7 days';
**Proposições lógicas** | $A$: O paciente está ativo (status = 'ativo') <br> $B$: O paciente tem uma ferida registrada<br> $C$: A mídia associada é uma imagem <br> $D$: A última imagem foi enviada há mais de 7 dias
**Expressão lógica proposicional** | $A \land B \land C \land D$
**Tabela Verdade** | <table> <thead> <tr> <th>$A$</th><th>$B$</th><th>$C$</th><th>$D$</th><th>$A \land B \land C \land D$</th> </tr> </thead> <tbody> <tr><td>V</td><td>V</td><td>V</td><td>V</td><td>V</td></tr> <tr><td>V</td><td>V</td><td>V</td><td>F</td><td>F</td></tr> <tr><td>V</td><td>V</td><td>F</td><td>V</td><td>F</td></tr> <tr><td>V</td><td>F</td><td>V</td><td>V</td><td>F</td></tr> <tr><td>F</td><td>V</td><td>V</td><td>V</td><td>F</td></tr> </tbody> </table>

### CONSULTA 2 – Verificar relação entre médicos e pacientes cadastrados por eles

Essa consulta retorna a quantidade de pacientes cadastrados por cada agente de saúde (médico). É útil para acompanhar a carga de atendimento de cada profissional e possibilitar uma melhor distribuição dos pacientes.


CONSULTA 2 | HANDBOOK E ASSEMBLELINE
--- | ---
**Expressão SQL** | SELECT a.id, a.nome, COUNT(paciente.id) AS qtd_pacientes<br>FROM agente a<br>JOIN paciente ON paciente.id_associatedAgente = a.id<br>GROUP BY a.id, a.nome;
**Proposições lógicas** | $A$: O agente é um médico<br>$B$: O médico cadastrou pacientes
**Expressão lógica proposicional** | $A \land B$
**Tabela Verdade** | <table> <thead> <tr><th>$A$</th><th>$B$</th><th>$A \land B$</th></tr></thead> <tbody> <tr><td>V</td><td>V</td><td>V</td></tr> <tr><td>V</td><td>F</td><td>F</td></tr> <tr><td>F</td><td>V</td><td>F</td></tr> <tr><td>F</td><td>F</td><td>F</td></tr> </tbody> </table>



### TABELA 3- UPDATE (ALERTA SISTEMA-PACIENTE)

Essa consulta atualiza o campo alerta das feridas para "urgência" quando a última foto enviada pelo paciente é mais antiga que 14 dias. Assim, o sistema marca automaticamente as feridas que estão sem acompanhamento recente, ajudando a priorizar casos potencialmente negligenciados.

CONSULTA 3 | HANDBOOK E ASSEMBLELINE
--- | ---
**Expressão SQL** | UPDATE ferida<br>SET  alerta = ‘urgência’<br>WHERE id IN (<br>  SELECT DISTINCT ON (f.id) f.id<br>  FROM ferida f<br>  JOIN midia m ON m.ferida_id = f.id<br>  ORDER BY f.id, m.data_envio DESC<br>WHERE m.data_envio < CURRENT_DATE - INTERVAL '14 days');
**Proposições lógicas** | $A$: O agente é um médico<br>$B$: O médico cadastrou pacientes
**Expressão lógica proposicional** | $A \land B$
**Tabela Verdade** | <table> <thead> <tr><th>$A$</th><th>$B$</th><th>$A \land B$</th></tr></thead> <tbody> <tr><td>V</td><td>V</td><td>V</td></tr> <tr><td>V</td><td>F</td><td>F</td></tr> <tr><td>F</td><td>V</td><td>F</td></tr> <tr><td>F</td><td>F</td><td>F</td></tr> </tbody> </table>


### TABELA 4- DELETE

Essa consulta remove do banco de dados os registros de pacientes cujo nome ou CPF estejam ausentes ou incorretos, ou ainda que tenham feridas cadastradas de forma incompleta. Essa limpeza evita dados inválidos e garante a integridade do sistema.

CONSULTA 4 | HANDBOOK E ASSEMBLELINE
--- | ---
**Expressão SQL** | DELETE FROM paciente<br>WHERE (nome IS NULL OR nome = '')<br>   OR (cpf IS NULL OR LENGTH(cpf::text) < 11)<br>   OR id IN (<br>       SELECT paciente_id<br>       FROM ferida<br> WHERE tipo IS NULL OR tipo = '');
**Proposições lógicas** | $A$: O agente cadastrou o nome do paciente errado<br>$B$: O agente cadastrou o CPF errado<br>$C$: O agente cadastrou a ferida errada
**Expressão lógica proposicional** | $A \lor B \lor C$
**Tabela Verdade** | <table> <thead> <tr><th>$A$</th><th>$B$</th><th>$C$</th><th>$A \lor B \lor C$</th></tr></thead> <tbody> <tr><td>V</td><td>V</td><td>V</td><td>V</td></tr> <tr><td>V</td><td>V</td><td>F</td><td>V</td></tr> <tr><td>V</td><td>F</td><td>V</td><td>V</td></tr> <tr><td>F</td><td>V</td><td>V</td><td>V</td></tr> <tr><td>V</td><td>F</td><td>F</td><td>V</td></tr> <tr><td>F</td><td>V</td><td>F</td><td>V</td></tr> <tr><td>F</td><td>F</td><td>V</td><td>V</td></tr> <tr><td>F</td><td>F</td><td>F</td><td>F</td></tr> </tbody> </table>



## 3.6. WebAPI e Endpoints (Sprints 3 e 4)

Durante as sprints 3 e 4, foi desenvolvida a **API RESTful** da aplicação, responsável por fornecer dados e funcionalidades essenciais para o sistema de acompanhamento clínico.

### Detalhamento da WebAPI

A WebAPI foi implementada utilizando **Node.js** e **Express**, com integração ao banco de dados **PostgreSQL**. Ela segue o padrão REST, permitindo a comunicação entre o frontend e o backend de forma padronizada e segura.  
A API realiza validação de dados, garante integridade referencial por meio de chaves estrangeiras e retorna respostas HTTP apropriadas para sucesso e erro.  
O sistema utiliza **CORS** para permitir requisições do frontend e pode empregar autenticação JWT para proteger rotas sensíveis, como as de agentes e dados clínicos.

A WebAPI está estruturada em módulos, cada um responsável por uma entidade principal do sistema (paciente, agente, ferida, prontuário, vídeos, etc). Os endpoints são organizados para permitir operações CRUD (Create, Read, Update, Delete) e refletem as relações entre as entidades no banco de dados.

#### Ligações entre Endpoints

- **Prontuários médicos** (`/medicalRecord`) estão ligados a pacientes e agentes, e podem conter informações sobre diferentes tipos de lesões (úlcera venosa, lesão por pressão, úlcera do pé diabético).
- **Feridas** (`/wound`) estão associadas a pacientes e agentes, e podem ser detalhadas em tipos específicos de lesão.
- **Vídeos** (`/api/video`) são cadastrados por agentes e ficam disponíveis para pacientes, sendo armazenados no Cloudinary e referenciados por links externos.
- **Pacientes** (`/patient`) podem ter múltiplos prontuários e feridas associados.
- **Tipos de lesão** (úlcera venosa, lesão por pressão, úlcera do pé diabético) estão ligados a um prontuário médico específico via chave estrangeira.

A seguir estão listados todos os endpoints desenvolvidos até o momento:

---

### `/medicalRecord`

- **Buscar todos os prontuários**
  - `GET /medicalRecord`
- **Buscar prontuário por ID**
  - `GET /medicalRecord/:id`
- **Criar prontuário**
  - `POST /medicalRecord`
  - Body:
    ```json
    {
      "wound_etiology": "Úlcera venosa",
      "location": "Perna direita"
    }
    ```
- **Atualizar prontuário**
  - `PUT /medicalRecord/:id`
- **Excluir prontuário**
  - `DELETE /medicalRecord/:id`

---

### `/patient`

- **Buscar todos os pacientes**
  - `GET /patient`
- **Buscar paciente por ID**
  - `GET /patient/:id`
- **Criar paciente**
  - `POST /patientRoutes`
  - Body:
    ```json
    {
      "name": "Maria Silva",
      "phone": "11987654321"
    }
    ```
- **Atualizar paciente**
  - `PUT /patient/:id`
- **Excluir paciente**
  - `DELETE /patient/:id`

---

### `/wound`

- **Buscar todas as feridas**
  - `GET /wound`
- **Buscar ferida por ID**
  - `GET /wound/:id`
- **Criar ferida**
  - `POST /wound`
  - Body:
    ```json
    {
      "pain_level": 7,
      "description": "..."
    }
    ```
- **Atualizar ferida**
  - `PUT /wound/:id`
- **Excluir ferida**
  - `DELETE /wound/:id`

---

### `/pressureUlcer`

- **Buscar todas as lesões por pressão**
  - `GET /pressureUlcer/`
- **Buscar lesão por ID**
  - `GET /pressureUlcer/:id`
- **Criar lesão por pressão**
  - `POST /pressureUlcer`
  - Body:
    ```json
    {
      "grade": "2° grau",
      "medical_record_id": 3
    }
    ```
- **Atualizar lesão**
  - `PUT /pressureUlcer/:id`
- **Excluir lesão**
  - `DELETE /pressureUlcer/:id`

---

### `/venous-ulcers`

- **Buscar todas as úlceras venosas**
  - `GET /venous-ulcers`
- **Buscar por ID**
  - `GET /venous-ulcers/:id`
- **Criar úlcera venosa**
  - `POST /venous-ulcers`
  - Body:
    ```json
    {
      "has_exudate": true
    }
    ```
- **Atualizar úlcera venosa**
  - `PUT /venous-ulcers/:id`
- **Excluir úlcera venosa**
  - `DELETE /venous-ulcers/:id`

---

### `/api/video`

- **Buscar todos os vídeos**
  - `GET /api/video`
  - Exemplo de resposta:
    ```json
    [
      {
        "id": 1,
        "external_link": "https://res.cloudinary.com/djupinm31/video/upload/suygo9qcvwijqawqlncj.mp4",
        "title": "Como higienizar a ferida",
        "description": "Aprenda a higienizar corretamente a ferida em casa."
      }
    ]
    ```
- **Buscar vídeo por ID**
  - `GET /api/video/:id`
- **Criar vídeo**
  - `POST /api/video`
  - Body:
    ```json
    {
      "external_link": "https://res.cloudinary.com/djupinm31/video/upload/suygo9qcvwijqawqlncj.mp4",
      "title": "Cuidados com feridas",
      "description": "Vídeo educativo sobre cuidados com feridas"
    }
    ```
- **Atualizar vídeo**
  - `PUT /api/video/:id`
- **Excluir vídeo**
  - `DELETE /api/video/:id`

---

### `/diabetic-foot-ulcers`

- **Buscar todas as úlceras do pé diabético**
  - `GET /diabetic-foot-ulcers`
- **Buscar por ID**
  - `GET /diabetic-foot-ulcers/:id`
- **Criar úlcera do pé diabético**
  - `POST /diabetic-foot-ulcers`
  - Body:
    ```json
    {
      "care": "Curativo com hidrogel",
      "medical_record_id": 3
    }
    ```
- **Atualizar úlcera do pé diabético**
  - `PUT /diabetic-foot-ulcers/:id`
- **Excluir úlcera do pé diabético**
  - `DELETE /diabetic-foot-ulcers/:id`

---

### Observações Técnicas

- **Autenticação:** Algumas rotas podem exigir autenticação JWT.
- **Validação:** Todos os dados recebidos são validados antes de serem persistidos.
- **Erros:** Respostas de erro seguem o padrão HTTP, com mensagens claras para o frontend.
- **Integração com Cloudinary:** O endpoint `/api/video` permite cadastrar e listar vídeos educativos hospedados no Cloudinary, facilitando o acesso do paciente a conteúdos de apoio ao tratamento.
- **CORS:** A WebAPI está configurada para aceitar requisições do frontend.
- **Relacionamentos:** As entidades possuem ligações via chaves estrangeiras, garantindo integridade dos dados e permitindo consultas relacionais (ex: buscar todas as feridas de um paciente, ou todos os vídeos disponíveis para um paciente).

# <a name="c4"></a>4. Desenvolvimento da Aplicação Web

## 4.1. Primeira versão da aplicação web (sprint 3)

Nesta primeira etapa do projeto, foi desenvolvido o esqueleto funcional da aplicação web com foco na estruturação em camadas e na implementação de operações básicas de CRUD (Create, Read, Update e Delete) para cada entidade principal do sistema. O objetivo foi garantir uma base sólida, organizada e escalável para as funcionalidades futuras.

Para organizar a aplicação de forma mais clara e profissional, adotamos o padrão de arquitetura em camadas, separando as responsabilidades em:

- Modelos (models): Definem a estrutura das entidades no banco de dados.

- Repositórios (repositories): Responsáveis por interações diretas com o banco de dados.

- Serviços (services): Contêm a lógica de negócio da aplicação.

- Controladores (controllers): Gerenciam as requisições e respostas HTTP.

- Rotas (routes): Definem os endpoints da API.

- Servidor (server.js): Ponto de entrada da aplicação.

Implementamos essas camadas para todas as tabelas do sistema, garantindo que cada entidade possua um CRUD completo. Isso permite que os dados possam ser criados, consultados, atualizados e removidos por meio de requisições HTTP.

Durante o desenvolvimento, enfrentamos alguns desafios, principalmente relacionados à:

- Organização correta das responsabilidades em cada camada.

- Integração entre as camadas (principalmente entre service e repository).

- Estruturação das rotas e tratamento de erros para garantir boas respostas da API.

- Integração entre branches.

Apesar dos obstáculos, conseguimos superá-los com pesquisas, testes e trabalho em equipe, o que resultou em uma aplicação funcional e bem organizada. Com a estrutura básica pronta e os CRUDes funcionando, a primeira versão da aplicação está concluída com sucesso. Este marco é importante, pois define a fundação sobre a qual construiremos as demais funcionalidades do sistema.

Próximos passos:
- Realizar o Front-End da aplicação.
- Realizar Testes Unitários.

Esse progresso inicial foi essencial para amadurecer o entendimento sobre o desenvolvimento de aplicações web estruturadas e preparar o caminho para funcionalidades mais complexas nas próximas etapas.

## 4.2. Segunda versão da aplicação web (sprint 4)

Durante esta etapa do projeto, o principal foco foi o desenvolvimento da camada de frontend e a sua integração com o backend, garantindo a comunicação entre as interfaces de usuário e os dados armazenados no servidor.

**Interface do Agente** <br>
Foram desenvolvidas todas as telas relacionadas ao fluxo de navegação do Agente, incluindo as funcionalidades de cadastro e login. O acesso à Home do Agente (homeAgent) é realizado a partir do Agent ID, que é passado como parâmetro na URL.<br>
<img width="949" alt="image" src="https://github.com/user-attachments/assets/5c1c7296-9a34-4935-8f76-1881a3a242fb" /><br>

Na Home, o agente pode visualizar uma lista de pacientes cadastrados. Ao clicar em um paciente específico, o sistema exibe a lista de feridas registradas, filtradas pelo Patient ID, e organizadas por data de envio.<br>
<img width="958" alt="image" src="https://github.com/user-attachments/assets/e89cb391-2213-47d5-bdbb-1c6e551cf222" /><br>

Dentro da página do paciente, o agente pode ser suas informações, essas puxadas by ID.<br>
<img width="946" alt="image" src="https://github.com/user-attachments/assets/07f4b6f9-7d35-439b-a8c7-714ce3a2ca69" /><br>


Também foi criada a tela de detalhes da ferida, porém, até o momento, os dados detalhados de cada ferida ainda não estão sendo carregados do backend, sendo uma das funcionalidades previstas para as próximas sprints.<br>
<img width="946" alt="image" src="https://github.com/user-attachments/assets/a761751e-e8eb-4580-92e7-e1db46cb0651" /> <br><br>


**Interface do Paciente** <br>
Na interface voltada para o Paciente, o acesso à Home do Paciente (homePatient) também é feito via Patient ID.<br> 
<img width="958" alt="image" src="https://github.com/user-attachments/assets/f044e487-7ecf-4c60-b8a1-5a575bba3635" /><br><br>

Nessa tela, o paciente pode: 
- Atualizar informações da ferida, preenchendo um formulário dedicado a isso;<br>
  <img width="946" alt="image" src="https://github.com/user-attachments/assets/e0a65839-11ba-47c5-a4cd-15d2d33527d0" /><br>

- Visualizar o histórico de feridas, listadas por data;<br>
 <img width="959" alt="image" src="https://github.com/user-attachments/assets/48aaa545-342f-4f7b-9207-d3e4e1f09f59" /><br>

- Ver os detalhes de cada ferida, porém assim como na interface do agente, essa funcionalidade enfrentou problemas de ser conectada com com backend para puxar as informações;<br>
  <img width="958" alt="image" src="https://github.com/user-attachments/assets/70e79696-2dd6-42b9-a1d4-87794488b25a" /><br>

- Página de vídeos informativos, que servirá como material de orientação e apoio ao paciente;<br>
  <img width="949" alt="image" src="https://github.com/user-attachments/assets/78721f84-bbe4-463b-97b0-504e0882300b" /><br>

- Página de alertas, que será desenvolvida como aprimoramento na próxima sprint.

Essas duas últimas páginas serão implementadas na próxima sprint como aprimoramente visando melhorar o acompanhamento e a comunicação com os usuários.

**Principal dificuldade encontrada**<br>
Na funcionalidade de atualização de informações da ferida, o paciente preenche um formulário, com campos como nível de dor, coceira, descrição da ferida e anexos de imagens. No entanto, durante os testes de integração com o backend, identificamos um problema que impede a finalização do envio desse formulário.

O erro ocorre porque o campo Agent ID, que é um parâmetro obrigatório no backend para o processamento e armazenamento das informações, está sendo enviado como null. Isso acontece porque, no fluxo atual da aplicação, o Agent ID não está sendo corretamente atribuído ou recuperado antes do envio dos dados, resultando na mensagem de erro: "Agent ID não encontrado".

Essa falha impede que os dados da atualização da ferida sejam salvos com sucesso no banco de dados. A correção desse problema foi registrada e será priorizada nas próximas sprints.

**Próximos Passos**<br>
As próximas etapas de desenvolvimento incluirão:
- Implementação da exibição dos detalhes completos das feridas;
- Implementação de um formulário para o agente cadastrar um novo paciente;
- Integração completa da página de alertas;
- Colocar os videos na página de Vídeos

## 4.3. Versão final da aplicação web (sprint 5)

*Descreva e ilustre aqui o desenvolvimento da última versão do sistema web, explicando brevemente o que foi entregue em termos de código e sistema. Utilize prints de tela para ilustrar. Indique as eventuais dificuldades e próximos passos.*

# <a name="c5"></a>5. Testes

## 5.1. Relatório de testes de integração de endpoints automatizados (sprint 4)
 Esta seção detalha a abordagem, a execução e a estrutura dos testes automatizados aplicados à Web API, visando garantir sua funcionalidade, robustez e conformidade com os requisitos. O framework Jest foi empregado para a suíte de testes.



_Abordagem e Execução dos Testes com Jest_




A estratégia de testes da Web API foi concebida para cobrir as principais camadas e funcionalidades da aplicação, garantindo a validação de comportamentos esperados e a detecção precoce de regressões. Os testes foram implementados com foco em:

- Testes Unitários: Validação isolada de componentes individuais da API, como funções, módulos e classes.
- Testes de Integração: Verificação da interação entre diferentes componentes da API e com serviços externos, simulando cenários de uso real.
- Simulação de Dependências (Mocks/Stubs): Utilização de recursos de mocking e stubbing do Jest para isolar a lógica da API sob teste e garantir a reprodutibilidade dos testes.

  
O Jest foi configurado em um ambiente Node.js, e os testes foram organizados de forma padronizada, com arquivos (.test.js ou .spec.js) próximos aos módulos que testam. Utilizamos describe() para agrupar testes logicamente e it() ou test() para casos de teste individuais, com asserções (expect()) para validar os resultados. O hook beforeEach() foi empregado para configurar e limpar o ambiente de teste antes de cada execução, garantindo isolamento. A ferramenta de cobertura de código integrada ao Jest foi utilizada para monitorar a porcentagem de código testado.



_Testes Unitários dos Serviços_


Os serviços que compõem a Web API foram submetidos a testes unitários automatizados para garantir seu correto funcionamento e a integridade da lógica de negócios. Abaixo, detalhamos os testes implementados, com foco nos casos confirmados pelo código fornecido:

diabeticFootUlcerService.test.js (Serviço de Úlcera do Pé Diabético)

Descrição: Testes para as operações CRUD (Create, Read, Update, Delete) para úlceras do pé diabético e seus cenários de sucesso e falha.
Casos de Teste Automatizados (Confirmados pelo Código):
Criação (createDiabeticFootUlcer):
Garante a criação de uma úlcera do pé diabético com dados válidos.
Leitura (getDiabeticFootUlcers, getDiabeticFootUlcerById):
Assegura o retorno de todas as úlceras do pé diabético.
Verifica o retorno de uma úlcera específica se existir pelo seu ID.
Confirma o lançamento de um erro se a úlcera não for encontrada pelo ID.
Atualização (updateDiabeticFootUlcer):
Valida a atualização de uma úlcera existente se encontrada pelo ID.
Assegura o lançamento de um erro se a úlcera não for encontrada para atualização.
Exclusão (deleteDiabeticFootUlcer):
Testa a exclusão de uma úlcera existente se encontrada pelo ID.
Confirma o lançamento de um erro se a úlcera não for encontrada para deletar.
Uso de beforeEach(): O hook beforeEach() é utilizado para limpar os mocks antes de cada teste, garantindo o isolamento.


agentService.test.js

Descrição: Testes unitários para o serviço de gestão de agentes.
footPhysicalExamService.test.js

Descrição: Testes unitários para o serviço de registro de exames físicos do pé.
mediaService.test.js

Descrição: Testes unitários para o serviço de gestão de mídias.
medicalRecordService.test.js

Descrição: Testes unitários para o serviço de prontuários médicos.
patientService.test.js

Descrição: Testes unitários para o serviço de gestão de pacientes.
pressureUlcerService.test.js

Descrição: Testes unitários para o serviço de úlceras por pressão.
venousUlcerService.test.js

Descrição: Testes unitários para o serviço de úlceras venosas.
videoService.test.js

Descrição: Testes unitários para o serviço de vídeos.
woundService.test.js

Descrição: Testes unitários para o serviço de gestão de feridas.
Relatório de Cobertura de Testes Jest
O relatório de cobertura de testes Jest oferece uma métrica quantitativa da abrangência dos testes automatizados. Ele indica a porcentagem de linhas, statements, funções e branches do código-fonte que foram executados durante a suíte de testes.

Nota: O relatório de cobertura de testes pode ser gerado executando-se jest --coverage no terminal do projeto. O resultado consolidado será incluído nesta seção quando disponível, ou através de um link para o relatório HTML gerado.

A aplicação rigorosa desta metodologia e o uso efetivo do Jest asseguram que a Web API é desenvolvida com alta qualidade, minimizando a introdução de bugs e garantindo a robustez e a confiabilidade da solução em produção.



## 5.2. Testes de usabilidade (sprint 5)
## 5.2 – Testes de Usabilidade

Nesta seção, são apresentados os testes de usabilidade realizados com usuários reais da aplicação, com o objetivo de avaliar a eficiência, clareza e navegabilidade das funcionalidades principais do sistema. Foram definidos três cenários representativos, testados por seis participantes com perfis variados, buscando observar o comportamento dos usuários, dificuldades enfrentadas e oportunidades de melhoria.

---

### Tarefas Avaliadas

---

### ✅ Tarefa 1: Cadastrar um agente

**Enunciado:**  
Suponha que você é o responsável por gerenciar os profissionais de saúde da clínica, está acessando o sistema administrativo e quer registrar um novo agente para integrar a equipe. Utilize o sistema para cadastrar um agente com suas informações completas.

**Etapas esperadas:**

1. Entrar como Profissional  
2. Clicar em “Não tem uma conta?”  
3. Preencher os dados do agente

**Resultados do teste:**

| Participante   | Resultado Geral | Etapa 1 | Etapa 2 | Etapa 3 |
|----------------|------------------|---------|---------|---------|
| Usuário 1      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 2      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 3      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 4      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 5      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 6      | Sucesso          | ✔️      | ✔️      | ✔️      |

---

### ✅ Tarefa 2: Consultar atualização da ferida

**Enunciado:**  
Suponha que você é um agente de saúde e precisa consultar a situação mais recente da ferida de um paciente. Utilize o sistema para localizar e visualizar a ferida mais atualizada.

**Etapas esperadas:**

1. Entrar como Profissional  
2. Acessar o paciente  
3. Escolher a ferida mais recente  
4. Consultar informações da ferida

**Resultados do teste:**

| Participante   | Resultado Geral     | Etapa 1 | Etapa 2 | Etapa 3 | Etapa 4 |
|----------------|----------------------|---------|---------|---------|---------|
| Usuário 1      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |
| Usuário 2      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |
| Usuário 3      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |
| Usuário 4      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |
| Usuário 5      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |
| Usuário 6      | Com dificuldade      | ✔️      | ✔️      | ✔️      | ❌      |

---

### ✅ Tarefa 3: Cadastrar uma ferida (como paciente)

**Enunciado:**  
Suponha que você é um paciente e quer registrar uma nova ferida para acompanhamento. Utilize o sistema para cadastrar essa nova ferida.

**Etapas esperadas:**

1. Entrar como Paciente  
2. Clicar em “Atualizar ferida”  
3. Preencher os dados da ferida

**Resultados do teste:**

| Participante   | Resultado Geral | Etapa 1 | Etapa 2 | Etapa 3 |
|----------------|------------------|---------|---------|---------|
| Usuário 1      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 2      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 3      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 4      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 5      | Sucesso          | ✔️      | ✔️      | ✔️      |
| Usuário 6      | Sucesso          | ✔️      | ✔️      | ✔️      |

---

### Contexto dos Testes

- **Número de participantes:** 6  
- **Ambiente:** Testes realizados com uso do sistema Web em navegador (Chrome), via computadores pessoais.  
- **Perfil dos participantes:** Usuários não familiarizados previamente com o sistema.  
- **Acompanhamento:** Cada teste foi acompanhado por um observador da equipe, que registrou verbalizações, erros e comentários dos usuários.

---

### Lista de Melhorias Identificadas

| Tarefa                | Severidade | Problema detectado                                                                 | Participantes afetados |
|-----------------------|------------|-------------------------------------------------------------------------------------|-------------------------|
| Cadastrar um agente   | Alta       | Falta de clareza entre “entrar como agente” e “entrar como paciente”               | Usuário 1, 2, 4         |
| Atualizar ferida      | Alta       | Atualização da ferida não apareceu no histórico                                    | Usuário 5               |
| Atualizar ferida      | Cosmética  | Faltou funcionalidade de zoom na imagem da ferida                                  | Usuário 2               |
| Interface do paciente | Cosmética  | Cores da logo na interface do paciente dificultam a leitura                        | Usuário 6               |
| Envio de imagem       | Média      | Botão de envio de foto pouco claro: falta texto “clique aqui para enviar uma foto” | Usuário 6               |
| Login e cadastro      | Média      | Falta de botão “Voltar” nas páginas de login e cadastro                            | Vários                  |
| Navegação             | Média      | Botão de voltar deveria estar no lado esquerdo para maior intuitividade            | Vários                  |

**Legenda de Severidade:**  
- **Alta**: Impede ou dificulta fortemente o uso da funcionalidade principal.  
- **Média**: Causa confusão ou frustração, mas não impede o uso.  
- **Cosmética**: Melhoria estética ou de conveniência, sem impacto funcional.

---

### Ações e Priorização

- Melhorias **críticas** priorizadas para a próxima sprint:
  - Separação visual mais clara entre login de agente e paciente.
  - Correção no backend da atualização de feridas no histórico.

- Melhorias **médias** com planejamento para próximas versões:
  - Inclusão de botão “Voltar” em login e cadastro.
  - Posição padrão à esquerda para o botão de voltar.
  - Indicação textual clara no botão de envio de foto.

- Melhorias **cosméticas** documentadas para revisão futura:
  - Zoom ao clicar em imagem de ferida.
  - Ajuste na cor da logo na interface do paciente.

---

### Conclusão

Com base nos testes realizados e nas análises apresentadas, fica evidente que os testes de usabilidade são fundamentais para identificar o estado atual da aplicação, além de revelar problemas que, embora nem sempre críticos, podem comprometer a experiência do usuário final.

Através da simulação de cenários reais e do acompanhamento direto de participantes com diferentes perfis, foi possível observar acertos no fluxo geral do sistema, mas também detectar pontos de atenção, como dificuldades de navegação, falhas de feedback visual e limitações funcionais em áreas específicas.

Esses achados não apenas orientam ajustes imediatos, como também servem de base estratégica para decisões de design e desenvolvimento nas próximas iterações do projeto. Ao priorizar melhorias com base na severidade dos problemas, garantimos que os aspectos mais impactantes sejam tratados com urgência, visando tornar o sistema cada vez mais eficiente, intuitivo e adequado às necessidades do público-alvo.

O link a seguir direciona à planilha original que foi utilizada durante os testes com os usuários: https://docs.google.com/spreadsheets/d/1Qz2NqgzyEMlqMPaLtozF9_FvGT-qk9YADfZOzjHx9H8/edit?usp=sharing

# <a name="c6"></a>6. Estudo de Mercado e Plano de Marketing (sprint 4)

## 6.1 Resumo Executivo

Nosso projeto se destaca pela acessibilidade e foco a um público-alvo específico, pacientes com feridas crônicas. A aplicação web tem como objetivo ser simples e intuitiva, com botões grandes e substituição de campos de texto por emojis que representam níveis de dor, facilitando o uso mesmo por quem tem dificuldades de leitura, escrita ou visão.

Outro diferencial importante é a parte médica da solução. O sistema conta com agentes de saúde especializados para tratamento de diabetes, e a interface dupla permite o envio de fotos das feridas, possibilitando um acompanhamento mais preciso e eficaz à distância. Além disso, o sistema envia alertas automáticos caso o paciente não envie atualizações, reforçando nosso comprometimento com o tratamento contínuo.

Em termos de estratégia, o objetivo inicial seria aplicar nosso serviço  em unidades de saúde pública, garantindo um funcionamento simples e eficiente. Assim, com base no funcionamento do programa e  nível de  feedback dos usuários e agentes, planejamos expandir gradualmente para todo o sistema de saúde pública do Brasil.

Sendo assim, buscamos subsídios públicos para geração de um fluxo de caixa mínimo para garantir o mantimento da infraestrutura tecnológica como o servidor por exemplo. Ademais, pretendemos investir pesado no marketing por meio de jornais, TV e redes sociais, além da realização de workshops para capacitação de agentes e divulgação da solução. 

Nosso foco principal é democratizar o acesso à saúde, para isso priorizamos uma divulgação para todo brasil, em relação ao lucro,  gerando um  impacto significativo na vida dos pacientes. Com isso identificamos uma oportunidade de penetrar em  um mercado pouco explorado como health tech, no qual nós oferecemos uma solução específica e acessível para pacientes com feridas crônicas. 

Em contrapartida há riscos como  a baixa adesão por parte do nosso público alvo com maior dificuldade digital, mesmo com uma interface acessível.  Por fim, isso nos  exige um trabalho com alta dedicação e com diversos testes como nosso público alvo antes de lançar publicamente

## 6.2 Análise de Mercado

*a) Visão Geral do Setor* 

A visão geral de um setor oferece um panorama amplo sobre suas principais características, tendências, desafios e oportunidades. Essa abordagem permite entender o funcionamento do mercado, suas dinâmicas e os fatores que impulsionam seu desenvolvimento. Compreender o setor como um todo é essencial para identificar caminhos estratégicos, antecipar mudanças e tomar decisões mais assertivas.

O setor de saúde tem passado por transformações significativas nas últimas décadas, impulsionado pela incorporação de novas tecnologias, mudanças demográficas e pela necessidade de tornar o atendimento mais eficiente e acessível. 

O mercado de healthtechs é composto por startups que utilizam tecnologia para enfrentar os principais desafios da área da saúde, desenvolvendo soluções inovadoras voltadas ao atendimento, diagnóstico, gestão e prevenção. Essas empresas se destacam em diferentes frentes, como telemedicina, prontuários eletrônicos, inteligência artificial e dispositivos de monitoramento, promovendo um sistema de saúde mais eficiente e conectado.

Com foco em tornar os serviços de saúde mais acessíveis, eficientes e personalizados, as healthtechs vêm transformando a forma como pacientes, profissionais e instituições se relacionam com o cuidado à saúde no Brasil e no mundo.

O mercado global de saúde digital deve alcançar US$ 1.080,21 bilhões até 2034, impulsionado por uma taxa composta de crescimento anual (CAGR) de 13,1% ao longo dos próximos dez anos. Esse avanço acelerado é resultado da adoção crescente de tecnologias como inteligência artificial, telemedicina, dispositivos vestíveis e soluções de análise de dados em saúde. (TOWARDS HEALTHCARE, 2024).

Diante desse cenário, fica evidente que as healthtechs desempenham um papel estratégico na modernização do setor de saúde, respondendo a desafios antigos com soluções inovadoras. O crescimento expressivo do mercado global de saúde digital reforça o potencial dessas tecnologias para transformar o cuidado à saúde, tornando-o mais integrado, eficiente e acessível às necessidades da população.

*b) Tamanho e Crescimento do Mercado* 

A análise do tamanho e do crescimento de um mercado é fundamental para compreender sua relevância econômica, identificar oportunidades de investimento e avaliar o potencial de expansão de produtos e serviços. Esses indicadores ajudam a mapear o desempenho histórico do setor, suas projeções futuras e os fatores que influenciam sua evolução, como inovações tecnológicas, mudanças no comportamento do consumidor e transformações no ambiente regulatório.

O setor de healthtechs no Brasil está em expansão, com 1.381 startups e US$ 1,4 bilhão em investimentos desde 2019. Elas usam tecnologia para melhorar o acesso, a gestão e o atendimento na saúde, com destaque para soluções como telemedicina e inteligência artificial. (SAÚDE BUSINESS, 2024)

Mesmo diante de desafios regulatórios e de integração, o setor apresenta alto potencial de crescimento, impulsionado pela digitalização, pelo envelhecimento populacional e pela crescente demanda por maior eficiência no sistema de saúde. Como mencionado anteriormente, estima-se que o mercado global atinja US$ 1.080,21 bilhões até 2034.

De acordo com o IBGE, a população do Brasil é de 203.080.756  pessoas. Considerando o último levantamento do Vigitel, uma pesquisa realizada pelo Ministério da Saúde em amostra representativa da população das 27 capitais, 10,2% dos entrevistados relataram ter diagnóstico de diabetes. Com base nesse percentual, estima-se que o número de pessoas com diabetes no país seja de aproximadamente 20 milhões. (SOCIEDADE BRASILEIRA DE DIABETES, 2024)

O Brasil integra a Região América do Sul e Central da Federação Internacional de Diabetes (IDF), composta por 19 países e territórios. O país figura entre as dez nações com o maior número de adultos (20 a 79 anos) com diabetes em todo o mundo. A análise indica que, a previsão para 2050, o Brasil possua 24 milhões de adultos, entre a faixa etária de  20 a 79 anos, com diabetes no Brasil. (International Diabetes Federation, 2025)

Estamos enfrentando um aumento significativo no número de pessoas com diabetes no Brasil, o que exige soluções eficientes no setor de saúde. As healthtechs têm grande potencial para ajudar nesse desafio, oferecendo tecnologias que melhoram o atendimento e a gestão. Embora haja desafios, a necessidade de modernização e o perfil demográfico do país destacam o papel estratégico das healthtechs no fortalecimento da saúde pública.

*c) Tendências de Mercado*

As tendências de mercado são mudanças e padrões que mostram a direção em que um setor ou a economia está evoluindo. Elas servem como referência para que empresas e profissionais possam identificar novas oportunidades, antecipar demandas e ajustar suas estratégias para se manterem relevantes e competitivos.

No setor da saúde, acompanhar essas tendências é essencial para desenvolver soluções que atendam às necessidades emergentes dos pacientes e dos serviços de saúde, além de promover inovação e eficiência em um mercado em constante transformação.

Segundo a SQUADRA (2025), o setor de saúde passará por transformações significativas em 2025, com destaque para tecnologias como inteligência artificial, big data e blockchain.

A IA está remodelando o setor de saúde, com aplicações em diagnósticos avançados, análise de imagens, previsão de doenças e automação de registros médicos. Ademais, assistentes virtuais e chatbots estão melhorando a interação com os pacientes, esclarecendo dúvidas, agendando consultas e gerenciando prontuários, eles otimizam operações e reduzem custos. Segundo a Precedence Research, o mercado global de IA na saúde deve ultrapassar US$ 180 bilhões até 2030.

Em telessaúde, o 5G viabiliza consultas remotas de alta qualidade, monitoramento em tempo real de pacientes e uso de dispositivos conectados. A tecnologia deve permitir respostas rápidas e avanços em procedimentos remotos, como cirurgias assistidas virtualmente.

O big data permite identificar padrões e tendências, apoiar diagnósticos clínicos e planejamento estratégico. Ferramentas preditivas ajudam a prever internações e identificar surtos em tempo real, promovendo economia e personalização do atendimento.

Diante dessas transformações, fica claro que as tendências tecnológicas estão redesenhando o setor de saúde e criando novas possibilidades para aprimorar o cuidado com o paciente, otimizar processos e reduzir custos. Acompanhar essas mudanças e investir em soluções inovadoras será essencial para que empresas e profissionais se mantenham competitivos e preparados para os desafios de um mercado em constante evolução.

## 6.3 Análise da Concorrência

*a) Principais Concorrentes*

Segundo Philip Kotler, a concorrência direta se refere a  produtos ou serviços semelhantes que visam resolver a mesma dor do público alvo. 

Na concorrência direta o serviço/produto disputa os mesmos perfis de clientes em questão de qualidade, preço, canal de venda e posicionamento.Já a concorrência indireta, vai resolver a mesma necessidade do público alvo, porém com um diferencial na solução que pode incluir: matéria prima, tecnologia ou modelo. (Kotler)

Tendo em vista  cenário de concorrentes diretos e indiretos, é altamente recomendado listar ambos para uma melhor análise e posicionamento de mercado de nosso serviço web.

Os concorrentes diretos de nosso serviço fora do cenário nacional são: Tissue Analytics (Philips) um app móvel com visão computacional que mede área da ferida e integra-se a prontuários; foca em hospitais de alta complexidade. 

Além do Swift Medical que utiliza tecnologia avançada com IA para tratamento de feridas e conta com:  análise da ferida, tamanho, estágio e  dashboards de evolução com relatórios que atendem home care e clínicas privadas(SWIFT MEDICAL.). Já no Brasil o maior concorrente direto é a “Brasil feridas” com serviço de teleconsulta e tele atendimento focado apenas em feridas além de contar com home care, assim se caracterizando com o serviço nacional que mais se assemelha com nossa proposta. (BRASIL FERIDAS)

Já os concorrentes indiretos são: Incor-TeleConsulta, Essencial Care, Einstein TeleMedicina e outros serviços que contam com teleconsultas e homecare mas sem o foco em feridas crónicas estes são generalistas englobando todas a maioria das áreas da saúde.

Ademais os apps de autocuidado também se englobam como concorrentes indireto por se tratar da mesma área mas com nicho diferente,como por exemplo os apps: Calm, Headspace, Fabulous e milhares de outros disponíveis, isso revela como a saúde na modernidade vem se digitalizando e abrindo espaço para mercados inexplorados como de health tech que ainda está longe de sua maturidade e  vem crescendo muito nos últimos anos. (DTTL PDF)

Tendo em vista essa análise, pode se concluir que, a concorrência indireta e direta   em sua maioria, conta com players que atuam no segmento privado. Logo  a inclusão de nossa solução como uma alternativa no setor público se mostra altamente benéfica, especialmente para o público-alvo que busca um atendimento gratuito e acessível para tratar suas dores.

*b) Vantagens Competitivas da Aplicação Web*

O principal diferencial da nossa aplicação em relação a outros serviços de telemedicina é o foco específico em pacientes com feridas crônicas. Ao adotar um nicho claro, conseguimos oferecer um atendimento mais eficaz, com profissionais especializados nesse tipo de cuidado. 

Além disso, valorizamos a relação entre agente e paciente, mantendo uma comunicação leve, acessível e atenciosa, essencial para o nosso público, majoritariamente composto por pessoas da terceira idade, que muitas vezes precisam de mais apoio para compreender e utilizar a tecnologia.

Outro ponto forte é o acompanhamento digital contínuo, com um sistema de notificações inteligente que alerta tanto o paciente quanto o agente caso as mídias das feridas não sejam enviadas dentro do prazo esperado. Isso garante regularidade no tratamento e maior segurança no monitoramento remoto.

Por fim, a acessibilidade é um pilar central da nossa solução. Desenvolvemos uma interface simples, com botões grandes e comandos intuitivos, pensando nas limitações visuais e motoras comuns desse público. Para facilitar ainda mais, substituímos textos complexos por uma comunicação baseada em emojis, permitindo que o paciente relate sua dor de forma prática e direta, sem a necessidade de escrever longos relatos.

## 6.4 Público-Alvo

A segmentação de mercado é a prática de dividir um mercado em grupos menores com características e necessidades semelhantes. Isso permite direcionar produtos ou serviços de forma mais eficaz a públicos específicos. No setor da saúde, segmentar ajuda a desenvolver soluções mais adequadas às realidades de pacientes e profissionais, promovendo melhor atendimento e maior impacto social.

O primeiro segmento é formado por pacientes portadores de feridas, como úlceras de pé diabético, úlceras venosas e lesões por pressão. Em sua maioria, têm comorbidades associadas, idade avançada e muitas delas dificuldade de locomoção.

Segundo a Sociedade Brasileira de Diabetes (2023), a incidência de úlcera do pé ao longo da vida de pacientes com diabetes varia entre 19% e 34%, com taxa de incidência anual de 2%.

Segundo Cruz, Caliri e Bernardes (2018), a ocorrência da úlcera venosa (UV) nos membros inferiores representa o estágio final de diversas anormalidades vasculares, sendo um importante problema de saúde pública, de longa duração, com alta taxa de recorrência e prevalente em pessoas idosas, frequentemente associada a comorbidades como diabetes mellitus, hipertensão arterial e obesidade.

No segundo segmento temos os profissionais da saúde, que se tratam de agentes comunitários de saúde, enfermeiros, residentes e médicos que atuam diretamente nas Unidades Básicas de Saúde (UBS). Esses profissionais são fundamentais para o monitoramento contínuo da saúde da população, sendo responsáveis pelo acompanhamento dos pacientes, pela detecção precoce de complicações e pela realização de ações de prevenção e cuidado no território.

Dessa forma, a segmentação de mercado da aplicação foca em atender pacientes com feridas crônicas e os profissionais da saúde responsáveis pelo seu cuidado, especialmente no contexto das Unidades Básicas de Saúde. Essa abordagem permite o desenvolvimento de soluções direcionadas, que contribuem para um acompanhamento mais eficiente, redução de complicações e melhoria da qualidade de vida dos pacientes.

## 6.5 Posicionamento

a- Proposta de Valor Única

O que torna nossa aplicação única é a forma em que resolvemos a dor de nosso cliente,  tornando simples e acessível o cuidado remoto de pacientes com feridas crônicas,  e dificuldades de leitura e visão. Nosso serviço não é só mais um aplicativo de saúde cheio de texto e menus complicados. O sistema foi desenhado com botões grandes e emojis no lugar das escalas tradicionais, facilitando a vida do usuário desde o primeiro acesso.

Além disso, nosso valor é garantir um acompanhamento contínuo para que o paciente não se sinta “largado" após sua primeira consulta.  O sistema envia lembretes automáticos sempre que o usuário  esqueça ou demora para atualizar as informações. Isso faz uma diferença real no tratamento, mostrando  comprometimento   e prevenindo futuras  complicações.

Para os agentes de saúde, a proposta também é clara: praticidade no atendimento. Os agentes de saúde diversas vezes tem suas rotinas cheias no pronto socorro, por isso um atendimento digital deve prevenir atendimentos presenciais desnecessários. No sistema os profissionais de saúde recebem fotos atualizadas das feridas em poucos cliques e podem agir rapidamente dependendo da situação.

b- Estratégia de Diferenciação

Nossa estratégia para nos destacarmos dos concorrentes foca em  três pontos chaves. Primeiro, decidimos atender um nicho específico,  pacientes com feridas crônicas no sistema público. Ao invés de tentar abraçar problemas gerais, escolhemos nos especificar, para assim conseguir dar uma atenção maior ao nosso cliente e com maior qualidade. 

Segundamente, vamos focar na relação agente-paciente e queremos realizar workshops e treinamentos especializados para selecionar profissionais com qualidade, atenciosidade e habilidade mínima de utilizar nosso programa. Com isso, nossa entrega foca em garantir agentes responsáveis e atenciosos para melhorar a experiência do nosso cliente final.

Por último, construímos nosso serviço planejado para o público da terceira idade em que muitas vezes tem alguma dificuldade utilizando tecnologia. Por isso mudamos  a acessibilidade com botões maiores e ao invés de escrever emojis para descrever a dor. Isso garante que o usuário não tenha nenhuma complexidade e reforça nossa ideia de simplicidade.

Em resumo, nosso posicionamento é sustentado por especialização, selecionamento e acessibilidade.. Isso garante uma solução eficiente, prática e adotada com facilidade no longo prazo.

## 6.6 Estratégia de Marketing 

*a) Produto/Serviço*

Nossa aplicação web “Conferidas", é uma plataforma web de telemedicina desenvolvida especificamente para o acompanhamento de feridas crônicas e diabetes, atendendo pacientes que, por limitações de mobilidade, permanecem afastados de unidades de saúde. 

A plataforma oferece uma dupla interface agente-paciente na qual é possível realizar acompanhamento remoto, lembretes de medicação e envio de foto do paciente ao especialista, histórico clínico do paciente e um atendimento humanizado via telemedicina. Esse produto vem solucionar a dor do cliente, que, em geral, é idoso com diabete ou alguma ferida crônica e não tem a menor capacidade de chegar a um hospital, clínica ou ubs e esperar na fila porque a ferida se agrava ao longo do tempo, ou seja, essa assistência digital resolve as dores do nosso cliente. Assim, nossa solução se diferencia das demais, devido ao foco na área de feridas crônicas ao invés de ser generalista, sem contar acessibilidade com botões grandes, emojis para repor textos descritivos e simplicidade para atender especialmente  o público alvo em maioria idoso com dificuldade de utilizar tecnologia 

*6.2 Preço*

Para garantir acessibilidade, o acesso ao site será gratuito para o usuário final. Nossos valores estão baseados na democratização do acesso à saúde. Por isso, cobrar pelo download do aplicativo ou oferecer planos pagos que priorizem determinados pacientes estaria em desacordo com nossos princípios e objetivos. Como alternativa de monetização, pretendemos vender nossa solução diretamente para CNPJs, incluindo instituições públicas como ONGs, órgãos governamentais e hospitais públicos, com exceção de casos em que já existam parcerias firmadas entre as instituições. Essa proposta também se aplica a empresas privadas interessadas em adotar nossa solução e colaborar para seu aprimoramento.

Para instituições privadas, pretendemos oferecer um modelo diferenciado, personalizado e mais aprimorado. Neste modelo, serão incluídas diversas opções de pagamento sendo trimestral,semestral e anual, para o CNPJ que escolher o plano anual terá acesso a uma versão bônus ainda mais aprimorada em relação aos outros planos, sem contar descontos e opção de mais parcelas na hora do pagamento. O serviço pago básico  inclui funcionalidades adicionais sob demanda, suporte premium, treinamentos específicos e possibilidade de integração avançada com outros sistemas internos e externos. Dessa forma, é possível atender ambas as esferas tanto públicas quanto privadas, isso nos proporcionará uma fonte extra de receita, garantindo sustentabilidade financeira e permitindo maior escalabilidade do nosso negócio.

*6.3 Praça (Distribuição)*

Nosso serviço terá disponibilidade via web em âmbito nacional, que roda em qualquer navegador. Ademais, a praça tem suas limitações devido ao escopo do projeto, para um paciente conseguir se cadastrar em nosso serviço, terá que recorrer a alguma unidade pública de saúde (seja presencialmente ou por telefone), e por-lá se cadastrar em nossa plataforma, o cadastro só será realizado caso o paciente esteja cadastrado no SUS. Para garantir a usabilidade, nos pretendemos  realizar workshops e eventos em alguns hospitais públicos para ensinar, tanto o agente quanto o paciente a utilizar nosso serviço

*6.4 Promoção*

Para a adesão da ferramenta o importante seria atividades de marketing voltadas para os profissionais da saúde e instituições, sendo estas mais focadas em canais institucionais e técnicos. Algumas possibilidades seriam as apresentações para órgãos públicos de saúde, workshops e treinamentos presenciais/online em UBSs para introdução da ferramenta, e participação em feiras e congressos de saúde pública e tecnologia. Ademais a divulgação também pode occorrer em canais com alto alcance como Tik Tok e Instagram, por mais que essa promoção não seja direcionada ao comprador final e não se adequam diretamente ao público alvo de idosos, continua sendo as plataformas com maior alcance mundial. Nesse contexto, mostrar de forma simples  como funcionaria nosso serviço de teleconsulta e como realizar o cadastro seria fundamental para conscientizar a população.  O conteúdo tende a alcançar os filhos e netos de possíveis pacientes que vão recomendar o aplicativo para seus pais/avós. 

# <a name="c7"></a>7. Conclusões e trabalhos futuros (sprint 5)

*Escreva de que formas a solução da aplicação web atingiu os objetivos descritos na seção 2 deste documento. Indique pontos fortes e pontos a melhorar de maneira geral.*

*Relacione os pontos de melhorias evidenciados nos testes com planos de ações para serem implementadas. O grupo não precisa implementá-las, pode deixar registrado aqui o plano para ações futuras*

*Relacione também quaisquer outras ideias que o grupo tenha para melhorias futuras*

# <a name="c8"></a>8. Referências (sprints 1 a 5)


LUCK, Heloisa. Liderança em gestão escolar. 4. ed. Petrópolis: Vozes, 2010. <br>
SOBRENOME, Nome. Título do livro: subtítulo do livro. Edição. Cidade de publicação: Nome da editora, Ano de publicação. <br>

INTELI. Adalove. Disponível em: https://adalove.inteli.edu.br/feed. Acesso em: 1 out. 2023 <br>
SOBRENOME, Nome. Título do site. Disponível em: link do site. Acesso em: Dia Mês Ano

FOLHA DE S.PAULO. Secretaria Municipal da Saúde de SP suspenderá cirurgias eletivas. Folha de S.Paulo, 15 mar. 2021. Disponível em: https://www1.folha.uol.com.br/equilibrioesaude/2021/03/secretaria-municipal-da-sau
de-de-sp-suspendera-cirurgias-eletivas.shtml. Acesso em: 29 abr. 2025.<br>

BRASIL. Ministério da Saúde. Plano de ações estratégicas para o enfrentamento das doenças crônicas e agravos não transmissíveis no Brasil, 2021-2030. Brasília: Ministério da Saúde, 2021. Disponível em: https://bvsms.saude.gov.br/bvs/publicacoes/plano_enfrentamento_doencas_cronicas_agravos_2021_2030.pdf. Acesso em: 29 abr. 2025.<br>


UNIVERSIDADE DE SÃO PAULO. Faculdade de Medicina. Programa de Pós-Graduação em Ciências do Sistema Musculoesquelético. Infraestrutura. Disponível em: https://www.musculoesqueleticousp.com.br/sobre-nos/infraestrutura/. Acesso em: 29 abr. 2025.​<br>

COELHO, Erika. USP-SP divulga relação candidato/vaga para seletivo de residência médica 2025. Estratégia MED, 8 nov. 2024. Disponível em: https://med.estrategia.com/portal/noticias/usp-sp-divulga-relacao-candidato-vaga-para-seletivo-de-residencia-medica-2025/​. Acesso em: 29 abr. 2025.<br>

CONTE, Juliana. Acesso à cirurgia plástica de reconstrução das mamas ainda é difícil no país. Portal Drauzio Varella, 15 fev. 2024. Disponível em: https://drauziovarella.uol.com.br/mulher/acesso-a-cirurgia-plastica-reparadora-das-mamas-ainda-e-dificil-no-pais/. Acesso em: 29 abr. 2025.<br>

ABCDT – Associação Brasileira dos Centros de Diálise e Transplante. HCFMUSP recebe R$ 53 milhões para modernização de 30 salas cirúrgicas. ABCDT, 2 jul. 2024. Disponível em: https://www.abcdt.org.br/2024/07/hcfmusp-recebe-r-53-milhoes-para-modernizacao-de-30-salas-cirurgicas/​. Acesso em: 29 abr. 2025.<br>

HOSPITAL DAS CLÍNICAS DA FACULDADE DE MEDICINA DA UNIVERSIDADE DE SÃO PAULO (HCFMUSP). Teleconsulta. São Paulo: HCFMUSP, [s.d.]. Disponível em: https://www.hc.fm.usp.br/hc/teleconsulta/teleconsulta​. Acesso em: 29 abr. 2025.<br>

TECIDOS. Banco de Tecidos do HC dobra capacidade para transplante de pele. USP Notícias, 1 out. 2012. Disponível em: https://www5.usp.br/noticias/saude-2/banco-de-tecidos-do-hc-dobra-capacidade-para-atender-paciente-que-precisa-de-pele/​. Acesso em: 29 abr. 2025.​<br>

RANKING SCIMAGO 2025. Faculdade de Medicina. FMUSP lidera Ranking SCImago 2025 na América Latina. FMUSP Notícias, 28 mar. 2025. Disponível em: https://www.fm.usp.br/fmusp/noticias/fmusp-lidera-ranking-scimago-2025-na-america-latina. Acesso em: 29 abr. 2025<br>

JORNAL DA USP. Hospital das Clínicas é o mais bem equipado do Brasil. 6 ago. 2020. Disponível em: https://jornal.usp.br/atualidades/hospital-das-clinicas-e-o-mais-bem-equipado-do-brasil-e-da-america-latina/. Acesso em: 29 abr. 2025.<br>

[1] Falanga, V. (2005). Wound healing and its impairment in the diabetic foot. The Lancet, 366(9498), 1736–1743. doi:10.1016/S0140-6736(05)67700-8<br>

[2] Armstrong, D. G., Boulton, A. J. M., & Bus, S. A. (2017). Diabetic foot ulcers and their recurrence. New England Journal of Medicine, 376(24), 2367–2375. doi:10.1056/NEJMra1615439<br>

[3] Sen, C. K. (2009). Wound healing essentials: Let there be oxygen. Wound Repair and Regeneration, 17(1), 1–18. doi:10.1111/j.1524-475X.2008.00436.x<br>

MORDOR INTELLIGENCE. *Brazil Wound Care Management Devices Market - Growth, Trends, and Forecasts (2025 - 2030)*. [Link](https://www.mordorintelligence.com/industry-reports/brazil-wound-care-management-devices-market)<br>

CRAVO, Alice et al. *Fila do SUS para cirurgia reparadora tem a maior espera...* O Globo. [Link](https://oglobo.globo.com/saude/noticia/2025/03/20/fila-do-sus-para-cirurgia-reparadora-tem-a-maior-espera-e-pode-levar-quase-dois-anos-em-media-veja-ranking.ghtml)<br>

COLTRO, P. S. et al. *Revista do Colégio Brasileiro de Cirurgiões*, 2011. [DOI](https://doi.org/10.1590/S0100-69912011000600003)<br>

Hospital Mãe de Deus. [Link](https://setorsaude.com.br/mae-de-deus-e-referencia-em-curativos-especiais/)<br>

Hospital Santa Izabel. [Link](https://www.hospitalsantaizabel.org.br/noticias/2016/12/13/hospital-santa-izabel-e-referencia-no-tratamento-de-feridas-cronicas.html)<br>

FERREIRA, M. C. et al. *Substitutos cutâneos...* [Scielo](https://www.scielo.br/j/rbcp/a/jTyrHmYJ4Qcf5H7rKZFWfgQ/)<br>

SMANIOTTO, P. H. S. et al. *Sistematização de curativos...* [Scielo](https://www.scielo.br/j/rbcp/a/mhg3d6bTNrg3ZgS9MYBLsCD/)

SOCIEDADE BRASILEIRA DE DIABETES. Diagnóstico e prevenção de úlceras no pé diabético. 2023. Disponível em: https://diretriz.diabetes.org.br/diagnostico-e-prevencao-de-ulceras-no-pe-diabetico/. Acesso em: 11 jun. 2025.

CRUZ, Clara Cayeiro; CALIRI, Maria Helena Larcher; BERNARDES, Rodrigo Magri. Características epidemiológicas e clínicas de pessoas com úlcera venosa atendidas em unidades municipais de saúde. ESTIMA, Brazilian Journal of Enterostomal Therapy, São Paulo, v. 16, e1218, 2018. Disponível em: https://doi.org/10.30886/estima.v16.496_PT. Acesso em: 11 jun. 2025.

SQUADRA Digital. 10 Tendências para o Setor de Saúde em 2025. Squadra, 14 jan. 2025. Disponível em: https://squadra.com.br/blog/10-tendencias-para-o-setor-de-saude-em-2025.html. Acesso em: 13 jun. 2025.

INTERNATIONAL DIABETES FEDERATION. Brazil: diabetes data and statistics. Diabetes Atlas. Disponível em: https://diabetesatlas.org/data-by-location/country/brazil/. Acesso em: 13 jun. 2025.

SAÚDE BUSINESS. Healthtech: O que é e como está revolucionando o mercado de saúde. Disponível em: https://www.saudebusiness.com/artigos/healthtech-o-que-e-e-como-esta-revolucionando-o-mercado-de-saude/. Acesso em: 12 jun. 2025.

TOWARDS HEALTHCARE. Digital health market is key to a healthier future. Towards Healthcare, 2024. Disponível em: https://www.towardshealthcare.com/insights/digital-health-market-is-key-to-a-healthier-future. Acesso em: 13 jun. 2025.


# <a name="c9"></a>Anexos

*Inclua aqui quaisquer complementos para seu projeto, como diagramas, imagens, tabelas etc. Organize em sub-tópicos utilizando headings menores (use ## ou ### para isso)*
