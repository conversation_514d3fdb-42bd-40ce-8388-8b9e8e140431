const pool = require('../config/db.js'); 

class PatientRepository {
  async create(patientData) {
    console.log('Repository: Tentando criar paciente no DB:', patientData.cpf);
    const query = `
      INSERT INTO patient (
        name, phone, age, cpf, password, agent_id
      )
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING id, name, phone, age, cpf, password, agent_id`; 

    const values = [
      patientData.name,
      patientData.phone,
      patientData.age,
      patientData.cpf,
      patientData.password,
      patientData.agent_id
    ];

    try {
      const result = await pool.query(query, values);
      console.log('Repository: Paciente criado no DB. ID:', result.rows[0].id);
      return result.rows[0];
    } catch (err) {
      console.error('Repository: Erro ao criar paciente no DB:', err);
      throw err;
    }
  }

  async findAll() {
    console.log('Repository: Buscando todos os pacientes no DB...');
    try {
      const result = await pool.query('SELECT id, name, phone, age, cpf, password, agent_id FROM patient ORDER BY id ASC');
      console.log(`Repository: Encontrados ${result.rows.length} pacientes no DB.`);
      return result.rows;
    } catch (err) {
      console.error('Repository: Erro ao buscar todos os pacientes no DB:', err);
      throw err;
    }
  }

  async findById(id) {
    console.log(`Repository: Buscando paciente por ID ${id} no DB...`);
    try {
      const result = await pool.query('SELECT id, name, phone, age, cpf, password, agent_id FROM patient WHERE id = $1', [id]);
      if (result.rows.length === 0) {
        console.log(`Repository: Paciente ID ${id} não encontrado no DB.`);
        return null;
      }
      console.log(`Repository: Paciente ID ${id} encontrado no DB.`);
      return result.rows[0];
    } catch (err) {
      console.error(`Repository: Erro ao buscar paciente por ID ${id} no DB:`, err);
      throw err;
    }
  }

  async findByCPF(cpf) {
    console.log(`Repository: Buscando paciente por CPF ${cpf} no DB...`);
    try {
      const result = await pool.query('SELECT id, name, phone, age, cpf, password, agent_id FROM patient WHERE cpf = $1', [cpf]);
      if (result.rows.length === 0) {
        console.log(`Repository: Paciente com CPF ${cpf} não encontrado no DB.`);
        return null;
      }
      console.log(`Repository: Paciente com CPF ${cpf} encontrado no DB.`);
      return result.rows[0];
    } catch (err) {
      console.error(`Repository: Erro ao buscar paciente por CPF ${cpf} no DB:`, err);
      throw err;
    }
  }

  async update(id, patientData) {
    console.log(`Repository: Tentando atualizar paciente ID ${id} no DB com dados:`, patientData);

    const fields = [];
    const values = [];
    let valueCounter = 1;

    for (const key in patientData) {
      if (key !== 'id' && patientData[key] !== undefined) {
         fields.push(`${key} = $${valueCounter++}`);
         values.push(patientData[key]);
      }
    }

    if (fields.length === 0) {
      console.log('Repository: Nenhum campo fornecido para atualização. Retornando paciente existente.');
      return this.findById(id);
    }

    values.push(id);

    const query = `
      UPDATE patient SET
        ${fields.join(', ')}
      WHERE id = $${valueCounter}
      RETURNING id, name, phone, age, cpf, password, agent_id`; 

    try {
      const result = await pool.query(query, values);
      if (result.rows.length === 0) {
        console.log(`Repository: Paciente ID ${id} não encontrado para atualização no DB.`);
        return null;
      }
      console.log(`Repository: Paciente ID ${id} atualizado no DB.`);
      return result.rows[0];
    } catch (err) {
      console.error(`Repository: Erro ao atualizar paciente ID ${id} no DB:`, err);
      throw err;
    }
  }

async remove(id) {
  console.log(`Repository: Tentando deletar registros relacionados do paciente ID ${id}...`);

  await pool.query('DELETE FROM medical_record WHERE patient_id = $1', [id]);

  console.log(`Repository: Tentando deletar paciente ID ${id} do DB...`);
  const result = await pool.query('DELETE FROM patient WHERE id = $1 RETURNING *', [id]);

  if (result.rows.length === 0) {
    return null;
  }
  return result.rows[0];
}

}
module.exports = new PatientRepository();