const Joi = require('joi');

const woundSchema = Joi.object({
  id: Joi.number().integer().positive().optional(),
  pain_level: Joi.number().min(1).max(10).required(), 
  date: Joi.date().iso().required(),
  itch: Joi.boolean().required(),
  description: Joi.string().max(255).optional(),
  location: Joi.string().max(100).optional(), 
  agent_id: Joi.number().integer().positive(),
  patient_id: Joi.number().integer().positive().required()
});

module.exports = woundSchema;