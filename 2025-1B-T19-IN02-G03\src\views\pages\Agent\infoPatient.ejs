<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Informações do Paciente</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .back-btn::before {
            content: "←";
            font-size: 16px;
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C 0%, #5DB85A 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .title-section h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .content {
            padding: 20px;
            background: #f5f5f5;
            min-height: calc(100vh - 140px);
        }

        .section {
            max-width: 800px;
            margin: 0 auto 30px auto;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #0D5F0C;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #3D963C;
        }

        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .info-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }

        .info-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        .info-card.full-width {
            grid-column: 1 / -1;
        }

        .info-label {
            font-size: 12px;
            font-weight: 600;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
            display: block;
        }

        .info-value {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            line-height: 1.3;
            word-break: break-word;
        }

        .info-value.large {
            font-size: 18px;
        }

        .info-value.boolean {
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .info-value.boolean::before {
            content: "✓";
            color: #3D963C;
            font-weight: bold;
        }

        .info-value.boolean.false::before {
            content: "✗";
            color: #dc3545;
        }

        .dimensions-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 10px;
        }

        .dimension-item {
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .dimension-label {
            font-size: 11px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }

        .dimension-value {
            font-size: 16px;
            font-weight: 600;
            color: #0D5F0C;
        }

        .btn-container {
            margin-top: 30px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .btn-primary {
            background-color: #2d7d2d;
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }

        .btn-primary:hover {
            background-color: #246824;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background-color: #6c757d;
            color: white;
            padding: 15px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s, transform 0.2s;
        }

        .btn-secondary:hover {
            background-color: #5a6268;
            transform: translateY(-1px);
        }

        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-badge.high {
            background-color: #dc3545;
            color: white;
        }

        .status-badge.medium {
            background-color: #ffc107;
            color: #000;
        }

        .status-badge.low {
            background-color: #28a745;
            color: white;
        }

        .status-badge.none {
            background-color: #6c757d;
            color: white;
        }

        /* Responsividade */
        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title-section h2 {
                font-size: 20px;
            }
            
            .cards-container {
                grid-template-columns: 1fr;
            }
            
            .dimensions-grid {
                grid-template-columns: 1fr;
            }
            
            .btn-container {
                grid-template-columns: 1fr;
            }
        }

        @media (min-width: 1024px) {
            .cards-container {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()">VOLTAR</button>
            <h1>Conferidas+</h1>
        </div>

        <div class="title-section">
            <h2>Informações do Paciente</h2>
        </div>

        <div class="content">
            <!-- Informações Básicas do Paciente -->
            <div class="section">
                <h3 class="section-title">Dados Pessoais</h3>
                <div class="cards-container">
                    <div class="info-card">
                        <span class="info-label">Nome</span>
                        <span class="info-value large"><%= patient.name %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Idade</span>
                        <span class="info-value large"><%= patient.age %> anos</span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">CPF</span>
                        <span class="info-value"><%= patient.cpf %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Telefone</span>
                        <span class="info-value"><%= patient.phone %></span>
                    </div>

                </div>
            </div>

            <!-- Informações do Prontuário Médico -->
            <% if (typeof medicalRecord !== 'undefined' && medicalRecord) { %>
            <div class="section">
                <h3 class="section-title">Avaliação da Ferida</h3>
                <div class="cards-container">
                    <div class="info-card">
                        <span class="info-label">Etiologia da Ferida</span>
                        <span class="info-value"><%= medicalRecord.wound_etiology %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Localização</span>
                        <span class="info-value"><%= medicalRecord.location %></span>
                    </div>

                    <div class="info-card full-width">
                        <span class="info-label">Dimensões da Ferida</span>
                        <div class="dimensions-grid">
                            <div class="dimension-item">
                                <div class="dimension-label">Largura</div>
                                <div class="dimension-value"><%= medicalRecord.width %> cm</div>
                            </div>
                            <div class="dimension-item">
                                <div class="dimension-label">Comprimento</div>
                                <div class="dimension-value"><%= medicalRecord.length %> cm</div>
                            </div>
                            <div class="dimension-item">
                                <div class="dimension-label">Profundidade</div>
                                <div class="dimension-value"><%= medicalRecord.depth %> cm</div>
                            </div>
                        </div>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Leito da Ferida</span>
                        <span class="info-value"><%= medicalRecord.wound_bed %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Borda da Ferida</span>
                        <span class="info-value"><%= medicalRecord.wound_edge %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Aparência</span>
                        <span class="info-value"><%= medicalRecord.appearance %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Temperatura</span>
                        <span class="info-value"><%= medicalRecord.temperature %></span>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Exsudato</h3>
                <div class="cards-container">
                    <div class="info-card">
                        <span class="info-label">Tipo de Exsudato</span>
                        <span class="info-value"><%= medicalRecord.exudate %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Características</span>
                        <span class="info-value"><%= medicalRecord.exudate_characteristics %></span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Intensidade</span>
                        <span class="info-value">
                            <span class="status-badge <%= medicalRecord.exudate_intensity.toLowerCase() %>">
                                <%= medicalRecord.exudate_intensity %>
                            </span>
                        </span>
                    </div>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Sintomas e Sinais</h3>
                <div class="cards-container">
                    <div class="info-card">
                        <span class="info-label">Odor</span>
                        <span class="info-value boolean <%= medicalRecord.odor ? 'true' : 'false' %>">
                            <%= medicalRecord.odor ? 'Presente' : 'Ausente' %>
                        </span>
                    </div>

                    <% if (medicalRecord.odor) { %>
                    <div class="info-card">
                        <span class="info-label">Intensidade do Odor</span>
                        <span class="info-value">
                            <span class="status-badge <%= medicalRecord.odor_intensity.toLowerCase() %>">
                                <%= medicalRecord.odor_intensity %>
                            </span>
                        </span>
                    </div>
                    <% } %>

                    <div class="info-card">
                        <span class="info-label">Edema</span>
                        <span class="info-value boolean <%= medicalRecord.edema ? 'true' : 'false' %>">
                            <%= medicalRecord.edema ? 'Presente' : 'Ausente' %>
                        </span>
                    </div>

                    <% if (medicalRecord.edema) { %>
                    <div class="info-card">
                        <span class="info-label">Intensidade do Edema</span>
                        <span class="info-value">
                            <span class="status-badge <%= medicalRecord.edema_intensity.toLowerCase() %>">
                                <%= medicalRecord.edema_intensity %>
                            </span>
                        </span>
                    </div>
                    <% } %>

                    <div class="info-card">
                        <span class="info-label">Dor</span>
                        <span class="info-value boolean <%= medicalRecord.pain ? 'true' : 'false' %>">
                            <%= medicalRecord.pain ? 'Presente' : 'Ausente' %>
                        </span>
                    </div>

                    <% if (medicalRecord.pain) { %>
                    <div class="info-card">
                        <span class="info-label">Intensidade da Dor</span>
                        <span class="info-value">
                            <span class="status-badge <%= medicalRecord.pain_intensity.toLowerCase() %>">
                                <%= medicalRecord.pain_intensity %>
                            </span>
                        </span>
                    </div>

                    <div class="info-card">
                        <span class="info-label">Frequência da Dor</span>
                        <span class="info-value"><%= medicalRecord.pain_frequency %></span>
                    </div>
                    <% } %>
                </div>
            </div>

            <div class="section">
                <h3 class="section-title">Fatores de Risco</h3>
                <div class="cards-container">
                    <div class="info-card">
                        <span class="info-label">Fatores de Risco</span>
                        <span class="info-value"><%= medicalRecord.risk_factors %></span>
                    </div>

                    <div class="info-card full-width">
                        <span class="info-label">Especificações dos Riscos</span>
                        <span class="info-value"><%= medicalRecord.risk_specifications %></span>
                    </div>
                </div>
            </div>
            <% } %>

            <div class="btn-container">
                <button type="button" class="btn-primary" onclick="editPatient()">
                    EDITAR INFORMAÇÕES
                </button>
                <% if (typeof medicalRecord !== 'undefined' && medicalRecord) { %>
                <button type="button" class="btn-secondary" onclick="viewHistory()">
                    HISTÓRICO MÉDICO
                </button>
                <% } %>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function editPatient() {
            alert('Redirecionando para edição...');
        }

        function viewHistory() {
            alert('Visualizando histórico médico...');
        }
    </script>
</body>
</html>