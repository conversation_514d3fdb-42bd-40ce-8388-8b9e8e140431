const patientService = require('../services/patientService');
const agentService = require('../services/agentService');

const homePatient = async (req, res) => {
  const id = req.params.id;
  try {
    const patient = await patientService.getPatientById(id);
    if (!patient) {
      return res.status(404).render('pages/patient/homePatient', { patient: null, error: 'Paciente não encontrado' });
    }
    res.render('pages/patient/homePatient', { patient });
  } catch (err) {
    res.status(500).render('pages/patient/homePatient', { patient: null, error: 'Erro ao buscar paciente' });
  }
};

const submitForm = async (req, res) => {
  const patientId = req.params.id;
  res.redirect(`/patient/homePatient/${patientId}`);
};

const loginPatient = (req, res) => {
  const patientId = req.query.patientId || req.body.patientId || 'defaultPatientId';
  res.render("pages/patient/loginPatient", { patient: patientId });
}

const processLogin = async (req, res) => {
  const { cpf } = req.body;

  try {
    const patient = await patientService.getPatientByCPF(cpf); 

    if (!patient) {
      return res.status(404).render('pages/patient/loginPatient', { patient: null, error: 'Paciente não encontrado' });
    }

    res.redirect(`/patient/homePatient/${patient.id}`);
  } catch (err) {
    console.error('Erro ao processar login:', err);
    res.status(500).render('pages/patient/loginPatient', {
      patient: null,
      error: 'Erro ao buscar paciente. Tente novamente.'
    });
  }
};


const showUpdateWoundForm = async (req, res) => {
  const patientId = req.params.id;
  console.log(`Buscando formulário para paciente ID: ${patientId}`);
  
  try {
    const patient = await patientService.getPatientById(patientId);
    if (!patient) {
      console.error(`Paciente com ID ${patientId} não encontrado`);
      return res.status(404).render('pages/patient/updateWound', { 
        error: 'Paciente não encontrado', 
        patient: null, 
        agent: null 
      });
    }

    let agent = null;
    try {
      agent = await agentService.getAgentByPatientId(patientId);
    } catch (agentErr) {
      console.warn(`Agente não encontrado para paciente ${patientId}:`, agentErr.message);
    }

    console.log(`Dados encontrados - Patient: ${patient.id}, Agent: ${agent ? agent.id : 'null'}`);
    
    res.render('pages/patient/updateWound', { patient, agent });
  } catch (err) {
    console.error(`Erro ao buscar dados para formulário do paciente ${patientId}:`, err);
    res.status(500).render('pages/patient/updateWound', { 
      error: 'Erro ao buscar dados do paciente', 
      patient: null, 
      agent: null 
    });
  }
};


async function createPatient(req, res) {
  console.log('Controller: Requisição para criar paciente.');
  try {
    const patient = await patientService.createPatient(req.body);
    res.status(201).json(patient);
  } catch (err) {
    console.error('Controller: Erro ao criar paciente:', err);
    res.status(err.statusCode || 400).json({ error: err.message || 'Erro ao criar paciente.' });
  }
}



async function getAllPatients(req, res) {
  console.log('Controller: Requisição para buscar todos os pacientes.');
  try {
    const patients = await patientService.getAllPatients();
    res.status(200).json(patients);
  } catch (err) {
    console.error('Controller: Erro ao buscar todos os pacientes:', err);
    res.status(err.statusCode || 500).json({ error: err.message || 'Erro ao buscar todos os pacientes.' });
  }
}

async function getPatientById(req, res) {
  const { id } = req.params;

  if (isNaN(id)) {
    console.warn(`Controller: ID inválido recebido: ${id}`);
    return res.status(400).json({ error: 'ID inválido: deve ser um número inteiro.' });
  }

  console.log(`Controller: Requisição para buscar paciente por ID: ${id}.`);
  try {
    const patient = await patientService.getPatientById(id);
    if (!patient) {
      return res.status(404).render('pages/patient/infoPatient', { patient: null, error: 'Paciente não encontrado' });
    }
    res.render('pages/patient/infoPatient', { patient });

  } catch (err) {
    console.error(`Controller: Erro ao buscar paciente por ID ${id}:`, err);
    res.status(500).render('pages/patient/infoPatient', { patient: null, error: 'Erro ao buscar paciente.' });
  }
}

async function updatePatient(req, res) {
  const { id } = req.params;
  console.log(`Controller: Requisição para atualizar paciente ID: ${id}.`);
  try {
    const patient = await patientService.updatePatient(id, req.body);
    res.status(200).json(patient);
  } catch (err) {
    console.error(`Controller: Erro ao atualizar paciente ID ${id}:`, err);
    res.status(err.statusCode || 400).json({ error: err.message || 'Erro ao atualizar paciente.' });
  }
}

async function deletePatient(req, res) {
  const { id } = req.params;
  console.log(`Controller: Requisição para deletar paciente ID: ${id}.`);
  try {
    await patientService.deletePatient(id);
    res.status(204).send();
  } catch (err) {
    console.error(`Controller: Erro ao deletar paciente ID ${id}:`, err);
    res.status(err.statusCode || 404).json({ error: err.message || 'Erro ao deletar paciente.' });
  }
}

module.exports = {
  loginPatient,
  showUpdateWoundForm,
  homePatient,
  submitForm,
  processLogin,
  createPatient,
  getAllPatients,
  getPatientById,
  updatePatient,
  deletePatient
};