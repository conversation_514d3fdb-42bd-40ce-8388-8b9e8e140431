<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vídeos - Conferidas</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .logout-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .welcome-section {
            background: linear-gradient(135deg, #3D963C, #4a7c59);
            padding: 12px 20px;
            text-align: center;
        }

        .welcome-title {
            color: white;
            font-size: 36px;
            font-weight: bold;
            margin-top: 10px;
            margin-bottom: 10px;
        }

        .search-question {
            color: white;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .content {
            padding: 20px;
            max-width: 600px;
            margin: 0 auto;
        }

        .videos-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .video-item {
            background-color: #0D5F0C;
            border-radius: 15px;
            padding: 30px 20px;
            text-align: center;
            color: white;
            transition: transform 0.2s, box-shadow 0.2s;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .video-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .video-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .video-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 20px;
            line-height: 1.3;
            text-transform: uppercase;
            color: white;
        }

        .video-thumbnail {
            width: 120px;
            height: 80px;
            border: 2px solid #3D963C;
            border-radius: 8px;
            margin: 0 auto 15px;
            display: block;
            background-color: rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #3D963C;
            font-size: 24px;
        }

        .watch-button {
            background: linear-gradient(135deg, #3D963C, #4a7c59);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: bold;
            cursor: pointer;
            text-transform: uppercase;
            transition: transform 0.2s;
            margin-top: 10px;
        }

        .watch-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        /* Ícones personalizados */
        .icon-video::before { content: "▶️"; }
        .icon-care::before { content: "🩹"; }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 1.3rem;
            }
            
            .welcome-title {
                font-size: 28px;
            }
            
            .search-question {
                font-size: 20px;
            }
            
            .content {
                padding: 15px;
            }
            
            .video-item {
                padding: 20px 15px;
                min-height: 160px;
            }
            
            .video-thumbnail {
                width: 100px;
                height: 60px;
            }
        }

        @media (max-width: 480px) {
            .videos-grid {
                gap: 15px;
            }
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .video-item {
            animation: fadeIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="logout-btn" onclick="goBack()">VOLTAR</button>
            <h1>Conferidas+</h1>
        </div>

        <div class="welcome-section">
            <h1 class="welcome-title">Vídeos Informativos</h1>
            <p class="search-question">Aprenda mais sobre cuidados</p>
        </div>

        <main class="content">
            <div class="videos-grid">
                <div class="video-item">
                    <div class="video-icon icon-care"></div>
                    <h2 class="video-title">Entenda a Ferida de<br>Úlcera Venosa</h2>
                    <div class="video-thumbnail">▶️</div>
                    <button class="watch-button" onclick="watchVideo('ulcera-venosa')">
                        Assistir
                    </button>
                </div>

                <div class="video-item">
                    <div class="video-icon icon-video"></div>
                    <h2 class="video-title">Como cuidar da<br>sua ferida</h2>
                    <div class="video-thumbnail">▶️</div>
                    <button class="watch-button" onclick="watchVideo('cuidar-ferida')">
                        Assistir
                    </button>
                </div>
            </div>
        </main>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function watchVideo(videoId) {
            console.log('Assistir vídeo:', videoId);
        }

        document.querySelectorAll('.video-item').forEach(item => {
            item.addEventListener('click', function(e) {
                if (!e.target.classList.contains('watch-button')) {
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = '';
                    }, 150);
                }
            });
        });

        document.querySelectorAll('.watch-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation();
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = '';
                }, 150);
            });
        });
    </script>
</body>
</html>