const mockRepository = {
  create: jest.fn(),
  getAll: jest.fn(),
  findById: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

jest.mock('../../repositories/footPhysicalExamRepository', () => mockRepository);
jest.mock('../../models/footPhysicalExamModel', () => ({
  validate: jest.fn(),
}));

const footPhysicalExamService = require('../../services/footPhysicalExamService');
const mockSchema = require('../../models/footPhysicalExamModel');

describe('footPhysicalExamService', () => {
  const exameValido = {
    sensibilidade: 'normal',
    perfusao: 'adequada',
    deformidades: false,
    observacoes: 'Sem alterações',
  };

  const exameInvalido = {
    sensibilidade: '',
    perfusao: '',
    deformidades: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    it('deve criar exame físico do pé com dados válidos', async () => {
      mockSchema.validate.mockReturnValue({ value: exameValido, error: undefined });
      const respostaMock = { id: 1, ...exameValido };
      mockRepository.create.mockResolvedValue(respostaMock);

      const resultado = await footPhysicalExamService.create(exameValido);

      expect(mockSchema.validate).toHaveBeenCalledWith(exameValido);
      expect(mockRepository.create).toHaveBeenCalledWith(exameValido);
      expect(resultado).toEqual(respostaMock);
    });

    it('deve lançar erro se os dados forem inválidos', async () => {
      mockSchema.validate.mockReturnValue({
        error: { details: [{ message: '"sensibilidade" é obrigatório' }] },
      });

      await expect(footPhysicalExamService.create(exameInvalido))
        .rejects.toThrow('"sensibilidade" é obrigatório');

      expect(mockRepository.create).not.toHaveBeenCalled();
    });
  });

  describe('getAll', () => {
    it('deve retornar todos os exames', () => {
      footPhysicalExamService.getAll();
      expect(mockRepository.getAll).toHaveBeenCalledTimes(1);
    });
  });

  describe('getById', () => {
    it('deve retornar exame se encontrado', async () => {
      const exame = { id: 1, ...exameValido };
      mockRepository.findById.mockResolvedValue(exame);

      const resultado = await footPhysicalExamService.getById(1);

      expect(mockRepository.findById).toHaveBeenCalledWith(1);
      expect(resultado).toEqual(exame);
    });

    it('deve lançar erro se exame não for encontrado', async () => {
      mockRepository.findById.mockResolvedValue(null);

      await expect(footPhysicalExamService.getById(99))
        .rejects.toThrow('Exam not found');
    });
  });

  describe('update', () => {
    it('deve atualizar exame com dados válidos', async () => {
      mockSchema.validate.mockReturnValue({ value: exameValido, error: undefined });
      const atualizado = { id: 1, ...exameValido };
      mockRepository.update.mockResolvedValue(atualizado);

      const resultado = await footPhysicalExamService.update(1, exameValido);

      expect(mockSchema.validate).toHaveBeenCalledWith(exameValido);
      expect(mockRepository.update).toHaveBeenCalledWith(1, exameValido);
      expect(resultado).toEqual(atualizado);
    });

    it('deve lançar erro se exame não for encontrado', async () => {
      mockSchema.validate.mockReturnValue({ value: exameValido, error: undefined });
      mockRepository.update.mockResolvedValue(null);

      await expect(footPhysicalExamService.update(99, exameValido))
        .rejects.toThrow('Exam not found');
    });

    it('deve lançar erro se dados inválidos forem fornecidos', async () => {
      mockSchema.validate.mockReturnValue({
        error: { details: [{ message: '"perfusao" é obrigatória' }] },
      });

      await expect(footPhysicalExamService.update(1, exameInvalido))
        .rejects.toThrow('"perfusao" é obrigatória');
    });
  });

  describe('remove', () => {
    it('deve deletar exame se encontrado', async () => {
      mockRepository.delete.mockResolvedValue(true);

      const resultado = await footPhysicalExamService.remove(1);

      expect(mockRepository.delete).toHaveBeenCalledWith(1);
      expect(resultado).toBe(true);
    });

    it('deve lançar erro se exame não for encontrado para deletar', async () => {
      mockRepository.delete.mockResolvedValue(false);

      await expect(footPhysicalExamService.remove(1))
        .rejects.toThrow('Exam not found');
    });
  });
});
