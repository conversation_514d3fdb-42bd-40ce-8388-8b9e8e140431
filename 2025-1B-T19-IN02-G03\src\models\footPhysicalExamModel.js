const Joi = require("joi");

const footPhysicalExamSchema = Joi.object({
  id: Joi.number().integer().positive().optional(),
  patient_id: Joi.number().integer().positive().required(),
  exam_date: Joi.date().required(),
  abnormal_toe_or_foot_shape: Joi.boolean().required(),
  callus: Joi.boolean().required(),
  mycosis: Joi.boolean().required(),
  ingrown_nails: Joi.boolean().required(),
  straight_nail_cut: Joi.boolean().required(),
  curved_nail_cut: Joi.boolean().required(),
  poor_foot_hygiene: Joi.boolean().required(),
  wears_synthetic_sock_or_none: Joi.boolean().required(),
  wears_inappropriate_footwear: Joi.boolean().required(),
  muscle_weakness: Joi.boolean().required(),
  macerated_or_dry_skin: Joi.boolean().required(),
  interdigital_fissure: Joi.boolean().required(),
  amputation: Joi.boolean().required(),
  hallux_valgus: Joi.boolean().required(),
  edema: Joi.boolean().required(),
  muscle_weakness_notes: Joi.string().max(255).required(),
  amputation_location: Joi.string().max(255).allow(null).optional(),
  diabetic_foot_ulcer_id: Joi.number().integer().positive().required(),
});

module.exports = footPhysicalExamSchema;
