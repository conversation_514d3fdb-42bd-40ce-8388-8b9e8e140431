require("dotenv").config();

const express = require("express");
const db = require("./config/db");
const PORT = process.env.PORT || 3002;
const cors = require("cors");
const session = require("express-session");

const app = express();
const path = require("path");

// Configuração do EJS
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Configuração da sessão
app.use(
  session({
    secret: "conferidas-secret-key",
    resave: false,
    saveUninitialized: true,
    cookie: { secure: false }, 
  })
);

const indexRoutes = require("./routes/index.js");
const prontuarioRoutes = require("./routes/medicalRecordRoutes.js");
const agentRoutes = require("./routes/agentRoutes.js");
const medicalRecordRoutes = require("./routes/medicalRecordRoutes.js");
const woundRoutes = require("./routes/woundRoutes.js");
const mediaRoutes = require("./routes/mediaRoutes.js");
const footPhysicalExamRoutes = require("./routes/footPhysicalExamRoutes.js");

const pressureUlcerRoutes = require("./routes/pressureUlcerRoutes.js");
const videoRoutes = require("./routes/videoRoutes.js");
const venousUlcerRoutes = require("./routes/venousUlcerRoutes.js");
const patientRoutes = require("./routes/patientRoutes.js");
const diabeticFootUlcerRoutes = require("./routes/diabeticFootUlcerRoutes.js");

app.use(express.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, "public")));
app.use("/assets", express.static(path.join(__dirname, "assets")));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));


app.use(cors());
app.use(express.json());
app.use("/", indexRoutes);
app.use("/venous-ulcers", venousUlcerRoutes);
app.use("/pressureUlcer", pressureUlcerRoutes);
app.use("/agent", agentRoutes);
app.use("/videos", videoRoutes);
app.use("/medicalRecord", medicalRecordRoutes);
app.use("/wound", woundRoutes);
app.use("/prontuario", prontuarioRoutes);
app.use("/patient", patientRoutes);
app.use("/diabetic-foot-ulcers", diabeticFootUlcerRoutes);
app.use("/mediaRoutes", mediaRoutes);
app.use("/footPhysicalExamRoutes", footPhysicalExamRoutes);

app.use((err, req, res, next) => {
  console.error("Erro global não capturado:", err);
  res
    .status(err.statusCode || 500)
    .json({ error: err.message || "Ocorreu um erro inesperado no servidor." });
});

app.listen(PORT, () => {
  console.log(`✅ Servidor rodando em http://localhost:${PORT}`);
});
