<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
    }

    .header {
        background-color: #4a7c59;
        color: white;
        padding: 15px 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .logo-section {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .logo {
        font-size: 24px;
        font-weight: bold;
    }

    .logo::before {
        content: "🏥";
        margin-right: 8px;
    }

    .welcome-text {
        font-size: 18px;
        margin-left: 20px;
    }

    .user-section {
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .user-info {
        text-align: right;
    }

    .user-name {
        font-weight: bold;
        font-size: 16px;
    }

    .user-role {
        font-size: 12px;
        opacity: 0.9;
    }

    .logout-btn {
        background-color: #357a3d;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: background-color 0.3s;
    }

    .logout-btn:hover {
        background-color: #2d6633;
    }

    .nav-menu {
        background-color: #357a3d;
        padding: 10px 20px;
    }

    .nav-links {
        display: flex;
        gap: 30px;
        list-style: none;
    }

    .nav-links a {
        color: white;
        text-decoration: none;
        padding: 8px 12px;
        border-radius: 4px;
        transition: background-color 0.3s;
    }

    .nav-links a:hover,
    .nav-links a.active {
        background-color: #4a7c59;
    }

    @media (max-width: 768px) {
        .header {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .welcome-text {
            margin-left: 0;
            font-size: 16px;
        }

        .nav-links {
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
        }
    }
</style>
<header class="header">
    <div class="logo-section">
        <div class="logo">CopFeridas</div>
        <div class="welcome-text">Bem-vindo, <%= user?.name || 'Paciente' %>!</div>
    </div>
    
    <div class="user-section">
        <div class="user-info">
            <div class="user-name"><%= user?.name || 'Paciente' %></div>
            <div class="user-role">Paciente</div>
        </div>
        <button class="logout-btn" onclick="logout()">Sair</button>
    </div>
</header>

<nav class="nav-menu">
    <ul class="nav-links">
        <li><a href="/homePatient" class="<%= page === 'home' ? 'active' : '' %>">Início</a></li>
        <li><a href="/updateWound" class="<%= page === 'update' ? 'active' : '' %>">Atualizar Ferida</a></li>
        <li><a href="/listMidia" class="<%= page === 'media' ? 'active' : '' %>">Minhas Mídias</a></li>
    </ul>
</nav>