const mediaService = require("../services/mediaService.js");


const mediaController = {
  async getMedia(req, res) {
    try {
      const list = await mediaService.getMedia();
      res.json(list);
    } catch (err) {
      console.error("Erro em getMedia:", err);
      res
        .status(500)
        .json({ error: err.message || "Erro interno do servidor" });
    }
  },

  async getMediaById(req, res) {
    try {
      const { id } = req.params;
      const item = await mediaService.getMediaById(id);
      res.json(item);
    } catch (err) {
      console.error("Erro em getMediaById:", err);
      res.status(404).json({ error: err.message || "Mídia não encontrada" });
    }
  },

  async createMedia(req, res) {
    try {
      const newItem = await mediaService.createMedia(req.body);
      res.status(201).json(newItem);
    } catch (err) {
      console.error("Erro em createMedia:", err);
      res.status(400).json({ error: err.message || "Erro ao criar mídia" });
    }
  },

  async updateMedia(req, res) {
    try {
      const { id } = req.params;
      const updated = await mediaService.updateMedia(id, req.body);
      res.json(updated);
    } catch (err) {
      console.error("Erro em updateMedia:", err);
      res.status(400).json({ error: err.message || "Erro ao atualizar mídia" });
    }
  },

  async deleteMedia(req, res) {
    try {
      const { id } = req.params;
      await mediaService.deleteMedia(id);
      res.status(204).send();
    } catch (err) {
      console.error("Erro em deleteMedia:", err);
      res.status(404).json({ error: err.message || "Erro ao deletar mídia" });
    } 
  },
};

module.exports = mediaController;
