<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detalhes do Paciente</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            min-height: 100vh;
        }

        .container {
            width: 100%;
            background-color: white;
            min-height: 100vh;
            position: relative;
        }

        .header {
            background: linear-gradient(135deg, #0D5F0C 0%, #3D963C 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: relative;
        }

        .header h1 {
            font-size: 1.5rem;
            font-weight: 600;
            margin: 0;
        }

        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 14px;
            transition: background-color 0.3s;
        }

        .back-btn:hover {
            background: rgba(255,255,255,0.3);
        }

        .back-btn::before {
            content: "←";
            font-size: 16px;
        }

        .title-section {
            background: linear-gradient(135deg, #3D963C 0%, #5DB85A 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .title-section h2 {
            font-size: 24px;
            font-weight: 600;
            margin: 0;
        }

        .content {
            padding: 30px 20px;
            background: white;
        }

        .content-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .patient-info {
            background: linear-gradient(135deg, #0D5F0C, #3D963C);
            color: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
        }

        .patient-info h3 {
            font-size: 1.8rem;
            font-weight: 600;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.2s;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background-color: #3D963C;
            color: white;
            margin-bottom: 20px;
        }

        .btn-primary:hover {
            background-color: #2d7a2b;
            transform: translateY(-1px);
        }

        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .card-header {
            background: linear-gradient(135deg, #0D5F0C, #3D963C);
            color: white;
            padding: 20px;
            font-size: 1.2rem;
            font-weight: 600;
        }

        .card-body {
            padding: 25px;
        }

        .section-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: #0D5F0C;
            margin-bottom: 15px;
        }

        .history-list {
            display: grid;
            gap: 10px;
        }

        .history-item {
            background: #f8f9fa;
            padding: 15px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s;
            border-left: 4px solid #3D963C;
        }

        .history-item:hover {
            background: #e8f5e8;
            transform: translateX(5px);
        }

        .history-date {
            font-weight: 600;
            color: #0D5F0C;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-item.placeholder {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .history-item.placeholder:hover {
            transform: none;
            background: #f8f9fa;
        }

        .history-item.placeholder .history-date {
            color: #888;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .content {
                padding: 20px 15px;
            }
            
            .header h1 {
                font-size: 1.3rem;
            }
            
            .title-section h2 {
                font-size: 20px;
            }
            
            .patient-info h3 {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <button class="back-btn" onclick="goBack()"> VOLTAR </button>
            <h1>Conferidas+</h1>
        </div>

        <div class="title-section">
            <h2>Detalhes do Paciente</h2>
        </div>

        <div class="content">
            <div class="content-container">
                <div class="patient-info">
                    <% if (patient) { %>
                        <h3 id="patientName"><%= patient.name %></h3>
                    <% } else if (error) { %>
                        <h3 id="patientName"><%= error %></h3>
                    <% } else { %>
                        <h3 id="patientName">Paciente não encontrado</h3>
                    <% } %>
                </div>

                <% if (patient) { %>
                <button type="button" class="btn btn-primary" style="width: auto; margin-top: 20px;" onclick="window.location.href='/agent/info/<%= patient.id %>'">
                    ➕ Informações do paciente
                </button>
                <% } %>

                <div class="card">
                    <div class="card-body">
                    <div class="section-title">
                        Histórico de registro das feridas:
                    </div>
                    <div href="/agent/wound/<%= patient.id %>" class="history-list">
                        <% if (woundUpdates && woundUpdates.length > 0) { %>
                        <% woundUpdates.forEach(function(update) { %>
                        <div class="history-item" onclick="openWoundDetails('<%= update.id %>')">
                            <div class="history-date">
                            Dia <%= new Date(update.date).toLocaleDateString('pt-BR') %>
                            </div>
                        </div>
                        <% }) %>
                        <% } else { %>
                        <div class="history-item placeholder">
                            <div class="history-date">
                            Nenhum registro encontrado
                            <span class="history-arrow">→</span>
                            </div>
                        </div>
                        <% } %>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <% if (!patient) { %>
        <script>
            document.getElementById('patientName').textContent = 'Paciente não encontrado';
        </script>
    <% } %>

    <script>
    function goBack() {
        window.location.href = '/agent/homeAgent';
    }

    function openWoundDetails(woundId) {
        if (woundId) {
            window.location.href = '/agent/wound/' + woundId;
        } else {
            alert('ID da ferida inválido!');
        }
    }

    const activeItems = document.querySelectorAll('.history-item:not(.placeholder)');
    
    activeItems.forEach(item => {
        item.addEventListener('click', function() {
            this.style.transform = 'translateX(4px) translateY(-1px)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });

    function setPatientName(name) {
        document.getElementById('patientName').textContent = name;
    }
    </script>
</body>
</html>