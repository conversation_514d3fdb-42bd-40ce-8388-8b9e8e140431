// repositories/prontuarioRepository.js
const pool = require("../config/db");

class medicalRecordRepository {
  async create(medical_Record) {
    const query = `
      INSERT INTO medical_Record (
        wound_etiology, location, width, length,
        depth, wound_bed, wound_edge, exudate, exudate_characteristics,
        exudate_intensity, odor, odor_intensity, appearance, temperature,
        edema, edema_intensity, pain, pain_intensity, pain_frequency,
        risk_factors, risk_specifications, agent_id, patient_id
      )
      VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14,
        $15, $16, $17, $18, $19, $20, $21, $22, $23
      )
      RETURNING *`;

    const values = [
      medical_Record.wound_etiology,
      medical_Record.location,
      medical_Record.width,
      medical_Record.length,
      medical_Record.depth,
      medical_Record.wound_bed,
      medical_Record.wound_edge,
      medical_Record.exudate,
      medical_Record.exudate_characteristics,
      medical_Record.exudate_intensity,
      medical_Record.odor,
      medical_Record.odor_intensity,
      medical_Record.appearance,
      medical_Record.temperature,
      medical_Record.edema,
      medical_Record.edema_intensity,
      medical_Record.pain,
      medical_Record.pain_intensity,
      medical_Record.pain_frequency,
      medical_Record.risk_factors,
      medical_Record.risk_specifications,
      medical_Record.agent_id,
      medical_Record.patient_id,
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  }

  async getAll() {
    const result = await pool.query("SELECT id,  wound_etiology, location, width, length, depth, wound_bed, wound_edge, exudate, exudate_characteristics, exudate_intensity, odor, odor_intensity, appearance, temperature, edema, edema_intensity, pain, pain_intensity, pain_frequency, risk_factors, risk_specifications, agent_id, patient_id FROM medical_Record");
    return result.rows;
  }

  async findById(id) {
    const result = await pool.query(
      "SELECT id,  wound_etiology, location, width, length, depth, wound_bed, wound_edge, exudate, exudate_characteristics, exudate_intensity, odor, odor_intensity, appearance, temperature, edema, edema_intensity, pain, pain_intensity, pain_frequency, risk_factors, risk_specifications, agent_id, patient_id FROM medical_Record WHERE id = $1",
      [id]
    );
    return result.rows[0];
  }

  async findByPatientId(patientId) {
  const result = await pool.query(
    "SELECT * FROM medical_Record WHERE patient_id = $1",
    [patientId]
  );
  return result.rows[0];
}
  async updateMedicalRecord(id, medical_Record) {
    const query = `
      UPDATE medical_record SET
      wound_etiology = $1,
      location = $2,
      width = $3,
      length = $4,
      depth = $5,
      wound_bed = $6,
      wound_edge = $7,
      exudate = $8,
      exudate_characteristics = $9,
      exudate_intensity = $10,
      odor = $11,
      odor_intensity = $12,
      appearance = $13,
      temperature = $14,
      edema = $15,
      edema_intensity = $16,
      pain = $17,
      pain_intensity = $18,
      pain_frequency = $19,
      risk_factors = $20,
      risk_specifications = $21,
      agent_id = $22,
      patient_id = $23
      WHERE id = $24
        RETURNING *`;

    const values = [
      medical_Record.wound_etiology,
      medical_Record.location,
      medical_Record.width,
      medical_Record.length,
      medical_Record.depth,
      medical_Record.wound_bed,
      medical_Record.wound_edge,
      medical_Record.exudate,
      medical_Record.exudate_characteristics,
      medical_Record.exudate_intensity,
      medical_Record.odor,
      medical_Record.odor_intensity,
      medical_Record.appearance,
      medical_Record.temperature,
      medical_Record.edema,
      medical_Record.edema_intensity,
      medical_Record.pain,
      medical_Record.pain_intensity,
      medical_Record.pain_frequency,
      medical_Record.risk_factors,
      medical_Record.risk_specifications,
      medical_Record.agent_id,
      medical_Record.patient_id,
      id,
    ];

    const result = await pool.query(query, values);
    return result.rows[0];
  }

  async delete(id) {
    const result = await pool.query(
      "DELETE FROM medical_Record WHERE id = $1 RETURNING *",
      [id]
    );
    return result.rows[0];
  }
}

module.exports = new medicalRecordRepository();
