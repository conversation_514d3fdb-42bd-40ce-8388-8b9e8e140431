<!DOCTYPE html>

<html lang="pt-BR">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Conferidas - Cadastro</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background-color: #f5f5f5;
        min-height: 100vh;
      }

      .container {
        width: 100%;
        background-color: white;
        min-height: 100vh;
        position: relative;
      }

      .header {
        background: linear-gradient(135deg, #0d5f0c 0%, #3d963c 100%);
        color: white;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
      }

      .header h1 {
        font-size: 1.5rem;
        font-weight: 600;
        margin: 0;
      }

      .back-btn {
        background: rgba(255, 255, 255, 0.2);
        border: none;
        color: white;
        padding: 8px 12px;
        border-radius: 20px;
        cursor: pointer;
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
        transition: background-color 0.3s;
      }

      .back-btn:hover {
        background: rgba(255, 255, 255, 0.3);
      }

      .back-btn::before {
        content: "←";
        font-size: 16px;
      }

      .title-section {
        background: linear-gradient(135deg, #3d963c 0%, #5db85a 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .title-section h2 {
        font-size: 24px;
        font-weight: 600;
        margin: 0;
      }

      .content {
        padding: 30px 20px;
        background: white;
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: calc(100vh - 140px);
      }

      .form-container {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        width: 100%;
        max-width: 400px;
      }

      .form-content {
        padding: 30px;
      }

      .form-title {
        font-size: 18px;
        font-weight: bold;
        color: #333;
        margin-bottom: 25px;
        text-align: center;
      }

      .form-group {
        margin-bottom: 20px;
      }

      .form-group label {
        display: block;
        margin-bottom: 8px;
        color: #333;
        font-size: 14px;
      }

      .form-group input {
        width: 100%;
        padding: 12px;
        border: 2px solid #ddd;
        border-radius: 5px;
        font-size: 14px;
        outline: none;
        transition: border-color 0.3s;
      }

      .form-group input:focus {
        border-color: #2d7d2d;
      }

      .btn-primary {
        width: 100%;
        background-color: #2d7d2d;
        color: white;
        padding: 12px;
        border: none;
        border-radius: 5px;
        font-size: 16px;
        font-weight: bold;
        cursor: pointer;
        transition: background-color 0.3s;
        margin-bottom: 20px;
      }

      .btn-primary:hover {
        background-color: #246824;
      }

      .links {
        text-align: center;
        font-size: 12px;
      }

      .links a {
        color: #666;
        text-decoration: none;
      }

      .links a:hover {
        text-decoration: underline;
      }

      .login-link {
        text-align: center;
        margin-top: 15px;
      }

      .login-link a {
        color: #2d7d2d;
        text-decoration: none;
        font-weight: bold;
      }

      .login-link a:hover {
        text-decoration: underline;
      }

      @media (min-width: 768px) {
        .form-container {
          max-width: 500px;
        }

        .form-content {
          padding: 40px;
        }

        .form-title {
          font-size: 22px;
        }

        .form-group input {
          padding: 15px;
          font-size: 16px;
        }

        .btn-primary {
          padding: 15px;
          font-size: 18px;
        }

        .header h1 {
          font-size: 1.3rem;
        }

        .title-section h2 {
          font-size: 20px;
        }
      }

      @media (min-width: 1024px) {
        .form-container {
          max-width: 600px;
        }

        .form-content {
          padding: 50px;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <button class="back-btn" onclick="goBack()">VOLTAR</button>
        <h1>Conferidas+</h1>
      </div>

      <div class="title-section">
        <h2>Cadastro de Agente</h2>
      </div>

      <div class="content">
        <div class="form-container">
          <div class="form-content">
            <div class="form-title">Faça o seu cadastro</div>

            <% if (error) { %>
            <div style="color: red; margin-bottom: 15px; text-align: center">
              <%= error %>
            </div>
            <% } %>

            <form action="/agent" method="POST">
              <div class="form-group">
                <label for="name">Nome:</label>
                <input type="text" id="name" name="name" required />
              </div>
              <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" required />
              </div>
              <div class="form-group">
                <label for="password">Senha:</label>
                <input type="password" id="password" name="password" required />
              </div>
              <div class="form-group">
                <label for="crm">Número de registro:</label>
                <input type="text" id="crm" name="crm" required />
              </div>

              <button type="submit" class="btn-primary">CADASTRAR</button>

              <div class="links">
                <a href="#">Termos de uso</a>
              </div>

              <div class="login-link">
                <a href="/agent/loginAgent">Já tem uma conta? Faça login</a>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>

    <script>
      function goBack() {
        window.location.href = "/";
      }
    </script>
  </body>
</html>