const express = require("express");
const router = express.Router();
const patientController = require("../controllers/patientController");
const woundController = require("../controllers/woundController");

router.get("/loginPatient", patientController.loginPatient);

router.post("/loginPatient", patientController.processLogin);


router.get('/', patientController.getAllPatients);
router.post('/', patientController.createPatient);
router.get("/homePatient/:id", patientController.homePatient);
router.get('/atualizar-ferida/:id', patientController.showUpdateWoundForm);
router.get('/:id', patientController.getPatientById);
router.put('/:id', patientController.updatePatient);
router.delete('/:id', patientController.deletePatient);
router.get('/updateWound/:id', patientController.showUpdateWoundForm);

router.get('/myWound/:woundId', woundController.showWoundDetails);



module.exports = router;