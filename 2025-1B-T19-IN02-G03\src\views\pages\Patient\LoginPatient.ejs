<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Conferidas - Login</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background-color: #4a7c59;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .container {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .logo {
            background-color: white;
            padding: 20px 40px;
            border-radius: 10px;
            margin-bottom: 40px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .logo h1 {
            color: #4a7c59;
            font-size: 48px;
            font-weight: bold;
        }

        .logo .plus {
            color: #81c784;
            font-size: 48px;
        }

        .login-form {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }

        .form-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 30px;
            color: #333;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #4a7c59;
        }

        .login-btn {
            width: 100%;
            background-color: #4a7c59;
            color: white;
            padding: 15px;
            border: none;
            border-radius: 5px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background-color: #3d6b4a;
        }

        .terms-link {
            text-align: center;
            color: #666;
            text-decoration: underline;
            cursor: pointer;
        }

        .terms-link:hover {
            color: #4a7c59;
        }
    </style>
</head>
<body>
    <div class="header"></div>
    
    <div class="container">
        <div class="logo">
            <h1>Conferidas<span class="plus">+</span></h1>
        </div>

        <div class="login-form">
            <h2 class="form-title">Acesse sua conta SUS:</h2>
            
            <form action="/patient/loginPatient" method="POST">
                <div class="form-group">
                    <label for="cpf">CPF:</label>
                    <input type="text" id="cpf" name="cpf" placeholder="000.000.000-00" required>
                </div>

                <div class="form-group">
                    <label for="senha">Senha:</label>
                    <input type="password" id="senha" name="senha" placeholder="Digite sua senha" required>
                </div>

                <button  type="submit" class="login-btn">ENTRAR</button>
            </form>

            <div class="terms-link">Termos de uso</div>
        </div>
    </div>

    <script>
        document.getElementById('cpf').addEventListener('input', function(e) {
            let value = e.target.value.replace(/\D/g, '');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d)/, '$1.$2');
            value = value.replace(/(\d{3})(\d{1,2})$/, '$1-$2');
            e.target.value = value;
        });
    </script>
</body>
</html>