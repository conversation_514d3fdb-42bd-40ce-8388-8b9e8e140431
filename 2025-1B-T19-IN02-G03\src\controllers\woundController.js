const woundService = require('../services/woundService');
const patientService = require('../services/patientService');
const mediaService = require('../services/mediaService');
const agentService = require('../services/agentService');

const woundController = {
  wound: async (req, res) => {
    try {
      const { patientId } = req.params;
      const patient = await patientService.getPatientById(patientId);
      const agent = await agentService.getAgentByPatientId(patientId);
      
      res.render('pages/agent/wound', { patient, agent });
    } catch (err) {
      res.status(500).send('Erro ao carregar paciente: ' + err.message);
    }
  },

  async getWounds(req, res) {
    try {
      const list = await woundService.getWounds();
      res.json(list);
    } catch (err) {
      res.status(500).json({ error: err.message });
    }
  },


  async getWoundById(id) {
    const wound = await woundRepository.findById(id);
    if (!wound) throw new Error('Wound not found');

    const media = await mediaService.getMediaByWoundId(id);

    return { wound, media };
},


  async listWoundsByPatient(req, res) {
    try {
      const { woundId } = req.params;
      const { patientId } = req.params;
      const wounds = await woundService.getWoundsByPatientId(patientId);

      wounds.forEach(w => console.log(w.date));

     const woundsSorted = wounds.slice().sort((a, b) => {
        const dateA = new Date(a.date);
        const dateB = new Date(b.date);

        if (isNaN(dateA) || isNaN(dateB)) return 0;

        return dateB.getTime() - dateA.getTime();
      });

      const patient = await patientService.getPatientById(patientId);


      res.render('pages/patient/listWounds', {  woundUpdates: woundsSorted, wounds, patient, patientId , woundId });
    } catch (err) {
      res.status(500).send('Erro ao buscar feridas: ' + err.message);
    }
  },

  async showWoundDetails(req, res) {
  try {
    const { woundId } = req.params;
    const { wound, media } = await woundService.getWoundById(woundId);

    if (Array.isArray(media)) {
      media.forEach(m => {
        m.path = m.path.replace(/\\/g, '/');
        if (!m.path.startsWith('/')) {
          m.path = '/' + m.path;
        }
      });
    }

    media.forEach(m => {
      m.path = m.path.replace(/\\/g, '/');
      if (!m.path.startsWith('/')) {
        m.path = '/' + m.path;
      }
    });

    res.render('pages/patient/myWound', { wound, media });
  } catch (err) {
    res.status(500).send('Erro ao buscar detalhes da ferida: ' + err.message);
  }
},


  

  async createWound(req, res) {
  try {
    console.log('req.file:', req.file); 
     
    if (!req.file) {
      return res.status(400).json({ error: 'Arquivo não recebido' });
    }
        console.log('Tipo do arquivo (mimetype):', req.file.mimetype);


    console.log('Dados do formulário:', req.body);

    const { 
      pain_level, 
      date, 
      itch, 
      description, 
      patient_id, 
      agent_id,
      localizacao  
    } = req.body;

    console.log('patient_id bruto:', patient_id);
    console.log('patient_id convertido:', Number(patient_id));

    if (!patient_id || patient_id === '' || patient_id === 'undefined') {
      console.error('patient_id está ausente ou vazio');
      return res.status(400).json({ 
        error: 'ID do paciente é obrigatório e não foi fornecido' 
      });
    }

    const patientIdNumber = Number(patient_id);
    if (isNaN(patientIdNumber) || patientIdNumber <= 0) {
      console.error('patient_id inválido:', patient_id);
      return res.status(400).json({ 
        error: 'ID do paciente deve ser um número válido maior que zero' 
      });
    }

    const patientExists = await patientService.getPatientById(patientIdNumber);
    if (!patientExists) {
      console.error('Paciente não encontrado:', patientIdNumber);
      return res.status(404).json({ 
        error: 'Paciente não encontrado no sistema' 
      });
    }

    if (!pain_level || !date || !description) {
      return res.status(400).json({ 
        error: 'Campos obrigatórios não preenchidos: dor, data e descrição são obrigatórios' 
      });
    }

    let itchValue = false;
    if (itch === 'true' || itch === true) {
      itchValue = true;
    }

    const woundData = {
      pain_level: Number(pain_level),
      date: date,
      itch: itchValue,
      description: description.trim(),
      location: localizacao || 'não especificado', 
      patient_id: patientIdNumber
    };

    if (agent_id && agent_id !== '' && !isNaN(Number(agent_id))) {
      woundData.agent_id = Number(agent_id);
    }

    console.log('Dados da ferida processados:', woundData);

    const wound = await woundService.createWound(woundData);
    console.log('Ferida criada com sucesso:', wound);

    if (req.file) {
      console.log('Arquivo recebido para salvar mídia:', req.file);
      try {
        const mediaData = {
          wound_id: wound.id,
          type: req.file.mimetype, 
          path: req.file.path.replace(/\\/g, '/'), 
        };

        console.log('Dados da mídia a serem inseridos:', mediaData);
        await mediaService.createMedia(mediaData);
      } catch (err) {
        console.error('Erro ao salvar mídia:', err);
        return res.status(400).json({ error: 'Erro ao salvar a imagem da ferida.' });
      }
    }


    res.redirect('/patient/homePatient/' + patient_id);

  } catch (err) {
    console.error('Erro ao criar ferida:', err);
    res.status(400).json({ error: err.message });
  }
},


  async updateWound(req, res) {
    try {
      const { id } = req.params;
      const updated = await woundService.updateWound(id, req.body);
      res.json(updated);
    } catch (err) {
      res.status(400).json({ error: err.message });
    }
  },

  async deleteWound(req, res) {
    try {
      const { id } = req.params;
      await woundService.deleteWound(id);
      res.status(204).send();
    } catch (err) {
      res.status(404).json({ error: err.message });
    }
  }
};

module.exports = woundController;