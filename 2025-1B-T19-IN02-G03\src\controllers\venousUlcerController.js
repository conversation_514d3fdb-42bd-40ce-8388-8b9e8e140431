// controllers/venousUlcerController.js
const venousUlcerService = require("../services/venousUlcerService");

async function createVenousUlcer(req, res) {
  try {
    const venousUlcer = await venousUlcerService.createVenousUlcer(req.body);
    res.status(201).json(venousUlcer);
  } catch (err) {
    res.status(400).json({ error: err.message });
  }
}

async function getVenousUlcers(req, res) {
  try {
    const venousUlcers = await venousUlcerService.getVenousUlcers();
    res.status(200).json(venousUlcers);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
}

async function getVenousUlcerById(req, res) {
  try {
    const venousUlcer = await venousUlcerService.getVenousUlcerById(
      req.params.id
    );
    res.status(200).json(venousUlcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function updateVenousUlcer(req, res) {
  try {
    const venousUlcer = await venousUlcerService.updateVenousUlcer(
      req.params.id,
      req.body
    );
    res.status(200).json(venousUlcer);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

async function deleteVenousUlcer(req, res) {
  try {
    await venousUlcerService.deleteVenousUlcer(req.params.id);
    res.status(200).json({ message: "Úlcera venosa excluída com sucesso" });
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
}

module.exports = {
  createVenousUlcer,
  getVenousUlcers,
  getVenousUlcerById,
  updateVenousUlcer,
  deleteVenousUlcer,
};
